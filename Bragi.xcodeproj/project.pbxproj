// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		9AFEF7742DFD9AB500F859F2 /* Kingfisher in Frameworks */ = {isa = PBXBuildFile; productRef = 9AFEF7732DFD9AB500F859F2 /* Kingfisher */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		17E05B2E2DFBD0EA0056EC16 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E05B182DFBD0E90056EC16 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E05B1F2DFBD0E90056EC16;
			remoteInfo = Bragi;
		};
		17E05B382DFBD0EA0056EC16 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E05B182DFBD0E90056EC16 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E05B1F2DFBD0E90056EC16;
			remoteInfo = Bragi;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		17E05B202DFBD0E90056EC16 /* Bragi.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Bragi.app; sourceTree = BUILT_PRODUCTS_DIR; };
		17E05B2D2DFBD0EA0056EC16 /* BragiTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BragiTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		17E05B372DFBD0EA0056EC16 /* BragiUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BragiUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		17E05B572DFBD2A80056EC16 /* Exceptions for "Bragi" folder in "Bragi" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 17E05B1F2DFBD0E90056EC16 /* Bragi */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		17E05B222DFBD0E90056EC16 /* Bragi */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				17E05B572DFBD2A80056EC16 /* Exceptions for "Bragi" folder in "Bragi" target */,
			);
			path = Bragi;
			sourceTree = "<group>";
		};
		17E05B302DFBD0EA0056EC16 /* BragiTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = BragiTests;
			sourceTree = "<group>";
		};
		17E05B3A2DFBD0EA0056EC16 /* BragiUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = BragiUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		17E05B1D2DFBD0E90056EC16 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9AFEF7742DFD9AB500F859F2 /* Kingfisher in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E05B2A2DFBD0EA0056EC16 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E05B342DFBD0EA0056EC16 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		17E05B172DFBD0E90056EC16 = {
			isa = PBXGroup;
			children = (
				17E05B222DFBD0E90056EC16 /* Bragi */,
				17E05B302DFBD0EA0056EC16 /* BragiTests */,
				17E05B3A2DFBD0EA0056EC16 /* BragiUITests */,
				17E05B212DFBD0E90056EC16 /* Products */,
			);
			sourceTree = "<group>";
		};
		17E05B212DFBD0E90056EC16 /* Products */ = {
			isa = PBXGroup;
			children = (
				17E05B202DFBD0E90056EC16 /* Bragi.app */,
				17E05B2D2DFBD0EA0056EC16 /* BragiTests.xctest */,
				17E05B372DFBD0EA0056EC16 /* BragiUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		17E05B1F2DFBD0E90056EC16 /* Bragi */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E05B412DFBD0EA0056EC16 /* Build configuration list for PBXNativeTarget "Bragi" */;
			buildPhases = (
				17E05B1C2DFBD0E90056EC16 /* Sources */,
				17E05B1D2DFBD0E90056EC16 /* Frameworks */,
				17E05B1E2DFBD0E90056EC16 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				17E05B222DFBD0E90056EC16 /* Bragi */,
			);
			name = Bragi;
			packageProductDependencies = (
				9AFEF7732DFD9AB500F859F2 /* Kingfisher */,
			);
			productName = Bragi;
			productReference = 17E05B202DFBD0E90056EC16 /* Bragi.app */;
			productType = "com.apple.product-type.application";
		};
		17E05B2C2DFBD0EA0056EC16 /* BragiTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E05B442DFBD0EA0056EC16 /* Build configuration list for PBXNativeTarget "BragiTests" */;
			buildPhases = (
				17E05B292DFBD0EA0056EC16 /* Sources */,
				17E05B2A2DFBD0EA0056EC16 /* Frameworks */,
				17E05B2B2DFBD0EA0056EC16 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E05B2F2DFBD0EA0056EC16 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E05B302DFBD0EA0056EC16 /* BragiTests */,
			);
			name = BragiTests;
			packageProductDependencies = (
			);
			productName = BragiTests;
			productReference = 17E05B2D2DFBD0EA0056EC16 /* BragiTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		17E05B362DFBD0EA0056EC16 /* BragiUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E05B472DFBD0EA0056EC16 /* Build configuration list for PBXNativeTarget "BragiUITests" */;
			buildPhases = (
				17E05B332DFBD0EA0056EC16 /* Sources */,
				17E05B342DFBD0EA0056EC16 /* Frameworks */,
				17E05B352DFBD0EA0056EC16 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E05B392DFBD0EA0056EC16 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E05B3A2DFBD0EA0056EC16 /* BragiUITests */,
			);
			name = BragiUITests;
			packageProductDependencies = (
			);
			productName = BragiUITests;
			productReference = 17E05B372DFBD0EA0056EC16 /* BragiUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		17E05B182DFBD0E90056EC16 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					17E05B1F2DFBD0E90056EC16 = {
						CreatedOnToolsVersion = 16.4;
					};
					17E05B2C2DFBD0EA0056EC16 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 17E05B1F2DFBD0E90056EC16;
					};
					17E05B362DFBD0EA0056EC16 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 17E05B1F2DFBD0E90056EC16;
					};
				};
			};
			buildConfigurationList = 17E05B1B2DFBD0E90056EC16 /* Build configuration list for PBXProject "Bragi" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 17E05B172DFBD0E90056EC16;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				9AFEF7722DFD9AB500F859F2 /* XCRemoteSwiftPackageReference "Kingfisher" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 17E05B212DFBD0E90056EC16 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17E05B1F2DFBD0E90056EC16 /* Bragi */,
				17E05B2C2DFBD0EA0056EC16 /* BragiTests */,
				17E05B362DFBD0EA0056EC16 /* BragiUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		17E05B1E2DFBD0E90056EC16 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E05B2B2DFBD0EA0056EC16 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E05B352DFBD0EA0056EC16 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		17E05B1C2DFBD0E90056EC16 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E05B292DFBD0EA0056EC16 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E05B332DFBD0EA0056EC16 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		17E05B2F2DFBD0EA0056EC16 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E05B1F2DFBD0E90056EC16 /* Bragi */;
			targetProxy = 17E05B2E2DFBD0EA0056EC16 /* PBXContainerItemProxy */;
		};
		17E05B392DFBD0EA0056EC16 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E05B1F2DFBD0E90056EC16 /* Bragi */;
			targetProxy = 17E05B382DFBD0EA0056EC16 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		17E05B3F2DFBD0EA0056EC16 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = NP2P722HAA;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		17E05B402DFBD0EA0056EC16 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = NP2P722HAA;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		17E05B422DFBD0EA0056EC16 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Bragi/Bragi.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NP2P722HAA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Bragi/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wuhengmin.Bragi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		17E05B432DFBD0EA0056EC16 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Bragi/Bragi.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NP2P722HAA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Bragi/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wuhengmin.Bragi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		17E05B452DFBD0EA0056EC16 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NP2P722HAA;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wuhengmin.BragiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Bragi.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Bragi";
			};
			name = Debug;
		};
		17E05B462DFBD0EA0056EC16 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NP2P722HAA;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wuhengmin.BragiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Bragi.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Bragi";
			};
			name = Release;
		};
		17E05B482DFBD0EA0056EC16 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NP2P722HAA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wuhengmin.BragiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Bragi;
			};
			name = Debug;
		};
		17E05B492DFBD0EA0056EC16 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NP2P722HAA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wuhengmin.BragiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Bragi;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		17E05B1B2DFBD0E90056EC16 /* Build configuration list for PBXProject "Bragi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E05B3F2DFBD0EA0056EC16 /* Debug */,
				17E05B402DFBD0EA0056EC16 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E05B412DFBD0EA0056EC16 /* Build configuration list for PBXNativeTarget "Bragi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E05B422DFBD0EA0056EC16 /* Debug */,
				17E05B432DFBD0EA0056EC16 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E05B442DFBD0EA0056EC16 /* Build configuration list for PBXNativeTarget "BragiTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E05B452DFBD0EA0056EC16 /* Debug */,
				17E05B462DFBD0EA0056EC16 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E05B472DFBD0EA0056EC16 /* Build configuration list for PBXNativeTarget "BragiUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E05B482DFBD0EA0056EC16 /* Debug */,
				17E05B492DFBD0EA0056EC16 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		9AFEF7722DFD9AB500F859F2 /* XCRemoteSwiftPackageReference "Kingfisher" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/Kingfisher";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		9AFEF7732DFD9AB500F859F2 /* Kingfisher */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9AFEF7722DFD9AB500F859F2 /* XCRemoteSwiftPackageReference "Kingfisher" */;
			productName = Kingfisher;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 17E05B182DFBD0E90056EC16 /* Project object */;
}
