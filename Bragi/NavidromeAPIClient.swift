//
//  NavidromeAPIClient.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import Foundation

// MARK: - Navidrome API 客户端

class NavidromeAPIClient: ObservableObject {
  @Published var isAuthenticated = false
  @Published var currentConfig: NavidromeConfig?
  
  private var urlSession = URLSession.shared
  
  func authenticate(serverURL: String, username: String, password: String) async throws {
    guard let url = URL(string: serverURL) else {
      throw NavidromeError.invalidServerURL
    }
    
    // 使用Subsonic API的ping端点进行认证测试
    var components = URLComponents(url: url.appendingPathComponent("rest/ping"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: username),
      URLQueryItem(name: "p", value: password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "<PERSON>rag<PERSON>"),
      URLQueryItem(name: "f", value: "json")
    ]
    
    guard let pingURL = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: pingURL)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        throw NavidromeError.authenticationFailed
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        // let error = subsonicResponse?["error"] as? [String: Any]
        // let message = error?["message"] as? String ?? "认证失败"
        throw NavidromeError.authenticationFailed
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      await MainActor.run {
        // Subsonic API不返回token，我们直接保存认证信息
        self.isAuthenticated = true
        self.currentConfig = NavidromeConfig(
          serverURL: serverURL,
          username: username,
          password: password,
          lastConnected: Date()
        )
      }
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  func fetchPlaylists() async throws -> [NavidromePlaylist] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    // 使用Subsonic API的getPlaylists端点获取播放列表
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getPlaylists"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json")
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取播放列表失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let playlists = subsonicResponse?["playlists"] as? [String: Any]
      let playlistArray = playlists?["playlist"] as? [[String: Any]] ?? []
      
      print("📡 fetchPlaylists 收到播放列表数据: \(playlistArray.count) 个")
      
      // 转换为NavidromePlaylist对象
      var result: [NavidromePlaylist] = []
      for playlistDict in playlistArray {
        if let playlist = parseSubsonicPlaylist(playlistDict) {
          result.append(playlist)
        } else {
          print("📡 解析播放列表失败: \(playlistDict)")
        }
      }
      
      print("📡 fetchPlaylists 成功解析播放列表: \(result.count) 个")
      
      return result
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  func fetchAlbums(limit: Int = 100, offset: Int = 0) async throws -> [NavidromeAlbum] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    // 使用Subsonic API的getAlbumList2端点获取专辑
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getAlbumList2"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "type", value: "newest"),
      URLQueryItem(name: "size", value: String(limit)),
      URLQueryItem(name: "offset", value: String(offset))
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取专辑失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let albumList2 = subsonicResponse?["albumList2"] as? [String: Any]
      let albumArray = albumList2?["album"] as? [[String: Any]] ?? []
      
      // 转换为NavidromeAlbum对象
      var albums: [NavidromeAlbum] = []
      for albumDict in albumArray {
        if let album = parseSubsonicAlbum(albumDict) {
          albums.append(album)
        }
      }
      
      return albums
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  func fetchArtists() async throws -> [NavidromeArtist] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    // 使用Subsonic API的getArtists端点获取艺术家
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getArtists"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json")
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取艺术家失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let artists = subsonicResponse?["artists"] as? [String: Any]
      let indexArray = artists?["index"] as? [[String: Any]] ?? []
      
      // 转换为NavidromeArtist对象
      var result: [NavidromeArtist] = []
      for indexDict in indexArray {
        let artistArray = indexDict["artist"] as? [[String: Any]] ?? []
        for artistDict in artistArray {
          if let artist = parseSubsonicArtist(artistDict) {
            result.append(artist)
          }
        }
      }
      
      return result
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  func fetchPlaylistSongs(playlistId: String) async throws -> [NavidromeSong] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    // 使用Subsonic API的getPlaylist端点获取播放列表歌曲
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getPlaylist"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "id", value: playlistId)
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取播放列表歌曲失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let playlist = subsonicResponse?["playlist"] as? [String: Any]
      let songArray = playlist?["entry"] as? [[String: Any]] ?? []
      
      // 转换为NavidromeSong对象
      var songs: [NavidromeSong] = []
      for songDict in songArray {
        if let song = parseSubsonicSong(songDict) {
          songs.append(song)
        }
      }
      
      return songs
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  func fetchArtistAlbums(artistId: String) async throws -> [NavidromeAlbum] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    // 使用Subsonic API的getArtist端点获取艺术家专辑
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getArtist"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "id", value: artistId)
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取艺术家专辑失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let artist = subsonicResponse?["artist"] as? [String: Any]
      let albumArray = artist?["album"] as? [[String: Any]] ?? []
      
      // 转换为NavidromeAlbum对象
      var albums: [NavidromeAlbum] = []
      for albumDict in albumArray {
        if let album = parseSubsonicAlbum(albumDict) {
          albums.append(album)
        }
      }
      
      return albums
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  func fetchAlbumSongs(albumId: String) async throws -> [NavidromeSong] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    // 使用Subsonic API的getAlbum端点获取专辑歌曲
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getAlbum"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "id", value: albumId)
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取专辑歌曲失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let album = subsonicResponse?["album"] as? [String: Any]
      let songArray = album?["song"] as? [[String: Any]] ?? []
      
      // 转换为NavidromeSong对象
      var songs: [NavidromeSong] = []
      for songDict in songArray {
        if let song = parseSubsonicSong(songDict) {
          songs.append(song)
        }
      }
      
      return songs
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  func fetchSongs(limit: Int = 100, offset: Int = 0) async throws -> [NavidromeSong] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }

    // 使用Subsonic API的getRandomSongs端点获取随机歌曲
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getRandomSongs"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "size", value: String(limit))
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      // 解析Subsonic响应
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      print("📡 fetchSongs API响应状态: \(status ?? "unknown")")
      print("📡 fetchSongs 完整响应结构: \(subsonicResponse?.keys.joined(separator: ", ") ?? "无")")
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取歌曲失败"
        print("📡 fetchSongs API错误: \(message)")
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        print("📡 fetchSongs API状态异常: \(status ?? "unknown")")
        throw NavidromeError.invalidResponse
      }
      
      // 尝试不同的可能数据结构
      var songArray: [[String: Any]] = []
      
      // 方法1: 检查 randomSongs 结构
      if let randomSongs = subsonicResponse?["randomSongs"] as? [String: Any] {
        print("📡 fetchSongs 找到randomSongs结构: \(randomSongs.keys.joined(separator: ", "))")
        if let songs = randomSongs["song"] as? [[String: Any]] {
          songArray = songs
        }
      }
      
      // 方法2: 检查直接的 song 数组
      if songArray.isEmpty, let songs = subsonicResponse?["song"] as? [[String: Any]] {
        print("📡 fetchSongs 找到直接的song数组")
        songArray = songs
      }
      
      // 方法3: 检查 randomSongs 是否直接是数组
      if songArray.isEmpty, let songs = subsonicResponse?["randomSongs"] as? [[String: Any]] {
        print("📡 fetchSongs randomSongs直接是数组")
        songArray = songs
      }
      
      print("📡 fetchSongs 收到歌曲数据: \(songArray.count) 首")
      
      // 转换为NavidromeSong对象
      var songs: [NavidromeSong] = []
      for songDict in songArray {
        if let song = parseSubsonicSong(songDict) {
          songs.append(song)
        } else {
          print("📡 解析歌曲失败: \(songDict)")
        }
      }
      
      // 按ID排序
      songs.sort { $0.id < $1.id }
      
      // 调试：输出前几首歌曲的ID以验证排序
      if !songs.isEmpty {
        let firstFew = songs.prefix(5).map { "\($0.id): \($0.displayTitle)" }
        print("📋 歌曲按ID排序，前5首: \(firstFew)")
      }
      
      print("📡 fetchSongs 最终返回歌曲: \(songs.count) 首")
      return songs
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  private func parseSubsonicSong(_ dict: [String: Any]) -> NavidromeSong? {
    guard let id = dict["id"] as? String,
          let title = dict["title"] as? String
    else {
      return nil
    }
    
    return NavidromeSong(
      id: id,
      title: title,
      artist: dict["artist"] as? String ?? "",
      album: dict["album"] as? String ?? "",
      track: dict["track"] as? Int,
      year: dict["year"] as? Int,
      genre: dict["genre"] as? String,
      duration: dict["duration"] as? Int ?? 0,
      size: dict["size"] as? Int ?? 0,
      suffix: dict["suffix"] as? String ?? "mp3",
      albumId: dict["albumId"] as? String ?? "",
      artistId: dict["artistId"] as? String ?? "",
      path: dict["path"] as? String ?? "",
      createdAt: dict["created"] as? String ?? "",
      updatedAt: dict["created"] as? String ?? "",
      coverArt: dict["coverArt"] as? String,
      starred: dict["starred"] as? String
    )
  }
  
  func getStreamURL(for song: NavidromeSong) -> URL? {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      return nil
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/stream"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "id", value: song.id)
    ]
    
    return components?.url
  }
  
  func getCoverArtURL(for coverArtId: String, size: Int = 200) -> URL? {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      return nil
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getCoverArt"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "id", value: coverArtId),
      URLQueryItem(name: "size", value: String(size))
    ]
    
    return components?.url
  }
  
  func logout() {
    Task { @MainActor in
      isAuthenticated = false
      currentConfig = nil
    }
  }
  
  // MARK: - 解析辅助方法
  
  private func parseSubsonicPlaylist(_ dict: [String: Any]) -> NavidromePlaylist? {
    guard let id = dict["id"] as? String,
          let name = dict["name"] as? String,
          let songCount = dict["songCount"] as? Int,
          let duration = dict["duration"] as? Int,
          let created = dict["created"] as? String
    else {
      return nil
    }
    
    return NavidromePlaylist(
      id: id,
      name: name,
      comment: dict["comment"] as? String,
      owner: dict["owner"] as? String,
      isPublic: dict["public"] as? Bool,
      songCount: songCount,
      duration: duration,
      created: created,
      changed: dict["changed"] as? String,
      coverArt: dict["coverArt"] as? String
    )
  }
  
  private func parseSubsonicAlbum(_ dict: [String: Any]) -> NavidromeAlbum? {
    guard let id = dict["id"] as? String,
          let name = dict["name"] as? String,
          let songCount = dict["songCount"] as? Int,
          let duration = dict["duration"] as? Int,
          let created = dict["created"] as? String
    else {
      return nil
    }
    
    return NavidromeAlbum(
      id: id,
      name: name,
      artist: dict["artist"] as? String,
      artistId: dict["artistId"] as? String,
      coverArt: dict["coverArt"] as? String,
      songCount: songCount,
      duration: duration,
      playCount: dict["playCount"] as? Int,
      created: created,
      year: dict["year"] as? Int,
      genre: dict["genre"] as? String
    )
  }
  
  private func parseSubsonicArtist(_ dict: [String: Any]) -> NavidromeArtist? {
    guard let id = dict["id"] as? String,
          let name = dict["name"] as? String,
          let albumCount = dict["albumCount"] as? Int
    else {
      return nil
    }
    
    return NavidromeArtist(
      id: id,
      name: name,
      coverArt: dict["coverArt"] as? String,
      albumCount: albumCount,
      starred: dict["starred"] as? String
    )
  }
  
  // MARK: - 歌单管理API
  
  /// 创建新歌单
  func createPlaylist(name: String, comment: String? = nil, isPublic: Bool = false, songIds: [String] = []) async throws -> String {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/createPlaylist"), resolvingAgainstBaseURL: false)
    var queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "name", value: name)
    ]
    
    if let comment = comment, !comment.isEmpty {
      queryItems.append(URLQueryItem(name: "comment", value: comment))
    }
    
    queryItems.append(URLQueryItem(name: "public", value: isPublic ? "true" : "false"))
    
    // 添加歌曲ID
    for songId in songIds {
      queryItems.append(URLQueryItem(name: "songId", value: songId))
    }
    
    components?.queryItems = queryItems
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "创建歌单失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let playlist = subsonicResponse?["playlist"] as? [String: Any]
      let playlistId = playlist?["id"] as? String ?? ""
      
      return playlistId
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  /// 添加歌曲到歌单
  func addSongToPlaylist(playlistId: String, songId: String) async throws {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/updatePlaylist"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "playlistId", value: playlistId),
      URLQueryItem(name: "songIdToAdd", value: songId)
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "添加歌曲到歌单失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  /// 从歌单中移除歌曲
  func removeSongFromPlaylist(playlistId: String, songIndex: Int) async throws {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/updatePlaylist"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "playlistId", value: playlistId),
      URLQueryItem(name: "songIndexToRemove", value: String(songIndex))
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "从歌单移除歌曲失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  /// 检查歌曲是否在歌单中
  func isSongInPlaylist(playlistId: String, songId: String) async throws -> Bool {
    do {
      let songs = try await fetchPlaylistSongs(playlistId: playlistId)
      return songs.contains { $0.id == songId }
    } catch {
      throw error
    }
  }
  
  /// 获取单首歌曲详情
  func fetchSong(songId: String) async throws -> NavidromeSong? {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getSong"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "id", value: songId)
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取歌曲详情失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      if let songDict = subsonicResponse?["song"] as? [String: Any] {
        return parseSubsonicSong(songDict)
      }
      
      return nil
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  // MARK: - 收藏(Star)管理API
  
  /// 收藏歌曲
  func starSong(songId: String) async throws {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/star"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "id", value: songId)
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "收藏歌曲失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  /// 取消收藏歌曲
  func unstarSong(songId: String) async throws {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/unstar"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json"),
      URLQueryItem(name: "id", value: songId)
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "取消收藏歌曲失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
  
  // MARK: - 获取收藏歌曲API
  
  /// 获取收藏的歌曲列表（支持分页）
  func fetchStarredSongs(offset: Int = 0, limit: Int = 500) async throws -> [NavidromeSong] {
    guard let config = currentConfig,
          let baseURL = URL(string: config.serverURL)
    else {
      throw NavidromeError.authenticationFailed
    }
    
    var components = URLComponents(url: baseURL.appendingPathComponent("rest/getStarred"), resolvingAgainstBaseURL: false)
    components?.queryItems = [
      URLQueryItem(name: "u", value: config.username),
      URLQueryItem(name: "p", value: config.password),
      URLQueryItem(name: "v", value: "1.16.1"),
      URLQueryItem(name: "c", value: "Bragi"),
      URLQueryItem(name: "f", value: "json")
    ]
    
    guard let url = components?.url else {
      throw NavidromeError.invalidServerURL
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    
    do {
      let (data, response) = try await urlSession.data(for: request)
      
      guard let httpResponse = response as? HTTPURLResponse else {
        throw NavidromeError.networkError("无效的服务器响应")
      }
      
      if httpResponse.statusCode == 401 {
        await MainActor.run {
          self.isAuthenticated = false
        }
        throw NavidromeError.tokenExpired
      } else if httpResponse.statusCode != 200 {
        throw NavidromeError.networkError("HTTP \(httpResponse.statusCode)")
      }
      
      let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
      let subsonicResponse = jsonResponse?["subsonic-response"] as? [String: Any]
      let status = subsonicResponse?["status"] as? String
      
      if status == "failed" {
        let error = subsonicResponse?["error"] as? [String: Any]
        let message = error?["message"] as? String ?? "获取收藏歌曲失败"
        throw NavidromeError.networkError(message)
      } else if status != "ok" {
        throw NavidromeError.invalidResponse
      }
      
      let starred = subsonicResponse?["starred"] as? [String: Any]
      let songsArray = starred?["song"] as? [[String: Any]] ?? []
      
      let songs = songsArray.compactMap { songDict in
        parseSubsonicSong(songDict)
      }
      
      print("📡 成功获取收藏歌曲: \(songs.count) 首")
      return songs
      
    } catch {
      if error is NavidromeError {
        throw error
      } else {
        throw NavidromeError.networkError(error.localizedDescription)
      }
    }
  }
}
