//
//  MusicLibraryCacheManager.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import Foundation

// MARK: - 音乐库缓存管理器

class MusicLibraryCacheManager: ObservableObject {
  static let shared = MusicLibraryCacheManager()
  
  // 缓存文件名
  private let playlistsCacheFile = "navidrome_playlists_cache.json"
  private let albumsCacheFile = "navidrome_albums_cache.json"
  private let artistsCacheFile = "navidrome_artists_cache.json"
  private let songsCacheFile = "navidrome_songs_cache.json"
  
  // 缓存过期时间（30分钟）
  private let cacheExpirationInterval: TimeInterval = 30 * 60
  
  private init() {}
  
  // MARK: - 缓存数据结构
  
  private struct CacheData<T: Codable>: Codable {
    let data: [T]
    let timestamp: Date
    let serverConfig: String // 服务器配置标识，用于区分不同服务器的缓存
    
    var isExpired: Bool {
      Date().timeIntervalSince(timestamp) > 30 * 60 // 30分钟过期
    }
  }
  
  // MARK: - 播放列表缓存
  
  func getCachedPlaylists(for serverConfig: String) -> [NavidromePlaylist]? {
    return getCachedData(fileName: playlistsCacheFile, serverConfig: serverConfig)
  }
  
  func savePlaylists(_ playlists: [NavidromePlaylist], for serverConfig: String) {
    saveCachedData(playlists, fileName: playlistsCacheFile, serverConfig: serverConfig)
  }
  
  func shouldUpdatePlaylists(_ newPlaylists: [NavidromePlaylist], cachedPlaylists: [NavidromePlaylist]) -> Bool {
    return !areArraysEqual(newPlaylists, cachedPlaylists)
  }
  
  // MARK: - 专辑缓存
  
  func getCachedAlbums(for serverConfig: String) -> [NavidromeAlbum]? {
    return getCachedData(fileName: albumsCacheFile, serverConfig: serverConfig)
  }
  
  func saveAlbums(_ albums: [NavidromeAlbum], for serverConfig: String) {
    saveCachedData(albums, fileName: albumsCacheFile, serverConfig: serverConfig)
  }
  
  func shouldUpdateAlbums(_ newAlbums: [NavidromeAlbum], cachedAlbums: [NavidromeAlbum]) -> Bool {
    return !areArraysEqual(newAlbums, cachedAlbums)
  }
  
  // MARK: - 艺术家缓存
  
  func getCachedArtists(for serverConfig: String) -> [NavidromeArtist]? {
    return getCachedData(fileName: artistsCacheFile, serverConfig: serverConfig)
  }
  
  func saveArtists(_ artists: [NavidromeArtist], for serverConfig: String) {
    saveCachedData(artists, fileName: artistsCacheFile, serverConfig: serverConfig)
  }
  
  func shouldUpdateArtists(_ newArtists: [NavidromeArtist], cachedArtists: [NavidromeArtist]) -> Bool {
    return !areArraysEqual(newArtists, cachedArtists)
  }
  
  // MARK: - 歌曲缓存
  
  func getCachedSongs(for serverConfig: String) -> [NavidromeSong]? {
    return getCachedData(fileName: songsCacheFile, serverConfig: serverConfig)
  }
  
  func saveSongs(_ songs: [NavidromeSong], for serverConfig: String) {
    saveCachedData(songs, fileName: songsCacheFile, serverConfig: serverConfig)
  }
  
  func shouldUpdateSongs(_ newSongs: [NavidromeSong], cachedSongs: [NavidromeSong]) -> Bool {
    return !areArraysEqual(newSongs, cachedSongs)
  }
  
  // MARK: - 通用缓存方法
  
  private func getCachedData<T: Codable>(fileName: String, serverConfig: String) -> [T]? {
    do {
      let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
      let cacheFile = documentsDirectory.appendingPathComponent(fileName)
      
      guard FileManager.default.fileExists(atPath: cacheFile.path) else {
        print("📦 缓存文件不存在: \(fileName)")
        return nil
      }
      
      let data = try Data(contentsOf: cacheFile)
      let cacheData = try JSONDecoder().decode(CacheData<T>.self, from: data)
      
      // 检查服务器配置是否匹配
      guard cacheData.serverConfig == serverConfig else {
        print("📦 服务器配置不匹配，忽略缓存: \(fileName)")
        return nil
      }
      
      // 检查是否过期
      if cacheData.isExpired {
        print("📦 缓存已过期: \(fileName)")
        return nil
      }
      
      print("📦 成功加载缓存: \(fileName), 数据量: \(cacheData.data.count)")
      return cacheData.data
      
    } catch {
      print("📦 加载缓存失败: \(fileName), 错误: \(error.localizedDescription)")
      return nil
    }
  }
  
  private func saveCachedData<T: Codable>(_ data: [T], fileName: String, serverConfig: String) {
    do {
      let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
      let cacheFile = documentsDirectory.appendingPathComponent(fileName)
      
      let cacheData = CacheData(data: data, timestamp: Date(), serverConfig: serverConfig)
      let encodedData = try JSONEncoder().encode(cacheData)
      try encodedData.write(to: cacheFile)
      
      print("📦 成功保存缓存: \(fileName), 数据量: \(data.count)")
      
    } catch {
      print("📦 保存缓存失败: \(fileName), 错误: \(error.localizedDescription)")
    }
  }
  
  private func areArraysEqual<T: Equatable>(_ array1: [T], _ array2: [T]) -> Bool {
    guard array1.count == array2.count else { return false }
    
    // 对于大数组，使用抽样比较来提高性能
    if array1.count > 100 {
      // 比较前10个、中间10个、后10个元素
      let frontIndices = Array(0..<min(10, array1.count))
      let middleStart = max(0, array1.count/2-5)
      let middleEnd = min(array1.count, array1.count/2 + 5)
      let middleIndices = Array(middleStart..<middleEnd)
      let backIndices = Array(max(0, array1.count-10)..<array1.count)
      
      let sampleIndices = Set(frontIndices + middleIndices + backIndices)
      
      for index in sampleIndices {
        if array1[index] != array2[index] {
          return false
        }
      }
      return true
    } else {
      // 小数组直接全部比较
      return array1 == array2
    }
  }
  
  // MARK: - 缓存管理
  
  func clearAllCache() {
    let cacheFiles = [playlistsCacheFile, albumsCacheFile, artistsCacheFile, songsCacheFile]
    let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    
    for fileName in cacheFiles {
      let cacheFile = documentsDirectory.appendingPathComponent(fileName)
      try? FileManager.default.removeItem(at: cacheFile)
    }
    
    print("📦 已清空所有音乐库缓存")
  }
  
  func getCacheInfo() -> String {
    let cacheFiles = [
      ("播放列表", playlistsCacheFile),
      ("专辑", albumsCacheFile),
      ("艺术家", artistsCacheFile),
      ("歌曲", songsCacheFile)
    ]
    
    let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    var info = "📦 音乐库缓存状态:\n"
    
    for (name, fileName) in cacheFiles {
      let cacheFile = documentsDirectory.appendingPathComponent(fileName)
      if FileManager.default.fileExists(atPath: cacheFile.path) {
        if let attributes = try? FileManager.default.attributesOfItem(atPath: cacheFile.path),
           let modificationDate = attributes[.modificationDate] as? Date
        {
          let formatter = DateFormatter()
          formatter.dateStyle = .short
          formatter.timeStyle = .short
          info += "\(name): ✅ (\(formatter.string(from: modificationDate)))\n"
        } else {
          info += "\(name): ✅\n"
        }
      } else {
        info += "\(name): ❌\n"
      }
    }
    
    return info
  }
  
  /// 生成服务器配置标识
  func generateServerConfigId(serverURL: String, username: String) -> String {
    return "\(username)@\(URL(string: serverURL)?.host ?? serverURL)"
  }
}
