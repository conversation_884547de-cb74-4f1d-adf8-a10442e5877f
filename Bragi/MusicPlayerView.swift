//
//  MusicPlayerView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import AVFoundation
import AVKit
import Combine
import SwiftUI

struct MusicPlayerView: View {
  @ObservedObject var audioManager = AudioPlayerManager.shared
  @ObservedObject var cacheManager = AudioCacheManager.shared
  @State private var navidromeClient: NavidromeAPIClient?
  @State private var showingMusicLibrary = false
  @State private var showingPlaylist = false
  @State private var showingCacheManagement = false
  @State private var isLiked = false
  @State private var isTogglingLike = false
  @State private var likeStatusCache: [String: Bool] = [:]
  @State private var coverDragOffset: CGFloat = 0
  @State private var artworkUpdateTrigger = 0
  @State private var sliderValue: Double = 0
  @State private var isDraggingSlider: Bool = false
  @State private var lastKnownDuration: Double = 0 // 跟踪时长变化
  @State private var navigationTargetArtist: String?
  @State private var navigationTargetAlbum: String?
  @State private var showingArtistDetail = false
  @State private var showingAlbumDetail = false
  @State private var selectedArtist: NavidromeArtist?
  @State private var selectedAlbum: NavidromeAlbum?
  
  // 歌单相关状态
  @State private var showingCreatePlaylist = false
  @State private var showingPlaylistSelection = false // 显示歌单选择页面
  @State private var availablePlaylists: [NavidromePlaylist] = []
  @State private var currentTrackId: String? = nil // 跟踪当前歌曲ID，用于状态更新
  
  // MARK: - 歌单状态管理器

  @StateObject private var playlistStateManager = PlaylistStateManager()
  
  var body: some View {
    GeometryReader { geometry in
      VStack(spacing: 0) {
        topNavigationBar
        Spacer()
        albumCarouselSection(geometry: geometry)
        Spacer()
        playbackControlsSection
      }
    }
    .background(Color(.systemBackground))
    .sheet(isPresented: $showingMusicLibrary) {
      MusicLibraryView(
        initialCategory: navigationTargetArtist != nil ? .artists : (navigationTargetAlbum != nil ? .albums : nil),
        targetArtistName: navigationTargetArtist,
        targetAlbumName: navigationTargetAlbum
      )
    }
    .onChange(of: showingMusicLibrary) { _, isShowing in
      // 当音乐库关闭时，重置导航目标
      if !isShowing {
        navigationTargetArtist = nil
        navigationTargetAlbum = nil
      }
    }
    .onChange(of: showingArtistDetail) { _, isShowing in
      // 当艺术家详情页关闭时，重置选中状态
      if !isShowing {
        selectedArtist = nil
      }
    }
    .onChange(of: showingAlbumDetail) { _, isShowing in
      // 当专辑详情页关闭时，重置选中状态
      if !isShowing {
        selectedAlbum = nil
      }
    }
    .sheet(isPresented: $showingPlaylist) {
      PlaylistView()
    }
    .sheet(isPresented: $showingCacheManagement) {
      CacheManagementView()
    }
    .sheet(isPresented: $showingArtistDetail) {
      if let artist = selectedArtist, let client = navidromeClient {
        NavigationView {
          ArtistDetailView(artist: artist, apiClient: client)
        }
      }
    }
    .sheet(isPresented: $showingAlbumDetail) {
      if let album = selectedAlbum, let client = navidromeClient {
        NavigationView {
          AlbumDetailView(album: album, apiClient: client, onDismissLibrary: nil)
        }
      }
    }
    .sheet(isPresented: $showingCreatePlaylist) {
      if let client = navidromeClient {
        CreatePlaylistView(apiClient: client) {
          // 歌单创建成功后重新加载歌单列表
          loadAvailablePlaylists()
          // 如果歌单选择页面还在显示，重新打开它
          DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            showingPlaylistSelection = true
          }
        }
      }
    }
    .sheet(isPresented: $showingPlaylistSelection) {
      if let client = navidromeClient {
        PlaylistSelectionView(
          apiClient: client,
          currentTrack: audioManager.currentTrack,
          availablePlaylists: $availablePlaylists,
          playlistStateManager: playlistStateManager,
          onCreatePlaylist: {
            showingPlaylistSelection = false
            showingCreatePlaylist = true
          },
          onPlaylistsUpdated: {
            loadAvailablePlaylists()
          }
        )
      }
    }
    .onReceive(audioManager.$currentTrack) { newTrack in
      // 切换歌曲时重置slider值
      if !isDraggingSlider {
        sliderValue = audioManager.currentTime
      }
      
      // 切换歌曲时确保navidromeClient状态正确
      if let track = newTrack, isNavidromeStream(track) {
        if navidromeClient == nil || !navidromeClient!.isAuthenticated {
          setupNavidromeClient()
        }
      }
      
      // 检查歌曲是否改变，如果改变则更新歌单状态
      let newTrackId = newTrack?.navidromeSongId ?? (newTrack != nil ? extractSongIdFromURL(newTrack!.url) : nil)
      if currentTrackId != newTrackId {
        currentTrackId = newTrackId
        artworkUpdateTrigger += 1 // 只在歌曲真正改变时更新封面触发器
        
        // 使用状态管理器更新歌单状态
        playlistStateManager.updateStatesForTrack(newTrackId, with: navidromeClient, playlists: availablePlaylists)
        
        // 更新收藏状态（无论是否是Navidrome歌曲都需要更新状态）
        // 切换到新歌曲时强制刷新收藏状态，不使用缓存
        updateLikeStatus(for: newTrack, forceRefresh: true)
      }
    }
    .onReceive(audioManager.$albumArtwork.removeDuplicates()) { _ in
      artworkUpdateTrigger += 1
    }
    .onReceive(audioManager.$artworkCacheUpdated.removeDuplicates()) { _ in
      artworkUpdateTrigger += 1
    }
    // 修复：降低时间更新频率，避免影响菜单使用
    .onReceive(audioManager.$currentTime.throttle(for: .seconds(0.5), scheduler: RunLoop.main, latest: true)) { newTime in
      // 只有在不拖动时才更新sliderValue
      if !isDraggingSlider {
        sliderValue = newTime
      }
    }
    .onReceive(audioManager.$duration) { newDuration in
      // 时长变化时，确保slider值在有效范围内
      if !isDraggingSlider && sliderValue > newDuration && newDuration > 0 {
        sliderValue = audioManager.currentTime
      }
      lastKnownDuration = newDuration
    }
    .onAppear {
      setupNavidromeClient()
      loadAvailablePlaylists()
      // 初始化当前歌曲的收藏状态
      updateLikeStatus(for: audioManager.currentTrack)
    }
    .onChange(of: navidromeClient?.isAuthenticated) { _, isAuthenticated in
      // 当Navidrome客户端认证状态改变时，清空缓存并更新收藏状态
      likeStatusCache.removeAll()
      if isAuthenticated == true {
        updateLikeStatus(for: audioManager.currentTrack)
      } else {
        isLiked = false
      }
    }
  }
    
  private var topNavigationBar: some View {
    HStack {
      // 移除左上角那个下拉箭头
      // Button(action: {}) {
      //   Image(systemName: "chevron.down")
      //     .font(.title2)
      //     .foregroundColor(.primary)
      // }
            
      VStack(spacing: 2) {
        Text("正在播放")
          .font(.caption)
          .foregroundColor(.secondary)
        Text("我的播放列表")
          .font(.footnote)
          .fontWeight(.semibold)
      }
            
      Spacer()
            
      Button(action: {
        showingMusicLibrary = true
      }) {
        Image(systemName: "music.note.list")
          .font(.title2)
          .foregroundColor(.primary)
      }
    }
    .padding(.horizontal, 20)
    .padding(.top, 10)
  }
    
  private func albumCarouselSection(geometry: GeometryProxy) -> some View {
    VStack(spacing: 30) {
      carouselView(geometry: geometry)
      songInfoView
      actionButtonsView
    }
  }
    
  private func carouselView(geometry: GeometryProxy) -> some View {
    let cardWidth = geometry.size.width * 0.75
        
    return ZStack {
      // 显示三张卡片：前一首、当前、下一首
      ForEach([-1, 0, 1], id: \.self) { offset in
        let trackIndex = audioManager.currentTrackIndex + offset
                
        if let track = getTrackAtIndex(trackIndex) {
          albumCard(track: track, geometry: geometry)
            .scaleEffect(offset == 0 ? 1.0 : 0.85)
            .opacity(offset == 0 ? 1.0 : 0.7)
            .offset(x: CGFloat(offset) * cardWidth + coverDragOffset)
            .zIndex(offset == 0 ? 1 : 0)
            .animation(.spring(response: 0.5, dampingFraction: 0.8), value: coverDragOffset)
        }
      }
    }
    .frame(width: cardWidth, height: cardWidth)
    .clipped()
    .gesture(
      DragGesture()
        .onChanged { value in
          // 限制拖拽范围
          let maxDrag = cardWidth * 0.8
          coverDragOffset = max(-maxDrag, min(maxDrag, value.translation.width))
        }
        .onEnded { value in
          let threshold: CGFloat = cardWidth * 0.3
                    
          withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            if value.translation.width > threshold, audioManager.currentTrackIndex > 0 {
              // 向右滑动 - 上一首
              audioManager.playPrevious()
            } else if value.translation.width < -threshold, audioManager.currentTrackIndex < audioManager.playlist.count - 1 {
              // 向左滑动 - 下一首
              audioManager.playNext()
            }
                        
            // 重置偏移
            coverDragOffset = 0
          }
        }
    )
  }
    
  private func albumCard(track: AudioFileInfo, geometry: GeometryProxy) -> some View {
    let cardSize = geometry.size.width * 0.75
        
    return Group {
      // 检查是否是Navidrome流媒体
      if isNavidromeStream(track), let client = navidromeClient {
        NavidromeAlbumArtView(
          track: track,
          apiClient: client,
          size: cardSize
        )
      } else if let artwork = audioManager.getArtworkForTrack(track) {
        Image(uiImage: artwork)
          .resizable()
          .aspectRatio(contentMode: .fill)
      } else {
        // 默认封面
        Rectangle()
          .fill(
            LinearGradient(
              colors: [
                Color.purple.opacity(0.6),
                Color.blue.opacity(0.4),
                Color.pink.opacity(0.3)
              ],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .overlay(
            VStack(spacing: 12) {
              Image(systemName: "music.note")
                .font(.system(size: 50))
                .foregroundColor(.white.opacity(0.8))
                            
              Text(track.name)
                .font(.caption)
                .foregroundColor(.white.opacity(0.9))
                .lineLimit(2)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 16)
            }
          )
      }
    }
    .frame(width: cardSize, height: cardSize)
    .cornerRadius(20)
    .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
  }
    
  private var songInfoView: some View {
    VStack(spacing: 8) {
      HStack(spacing: 8) {
        Text(audioManager.currentTrack?.name ?? "夜空中最亮的星")
          .font(.title2)
          .fontWeight(.bold)
          .foregroundColor(.primary)
        
        // 缓存状态指示器
        if let currentTrack = audioManager.currentTrack,
           !currentTrack.url.isFileURL
        {
          // 生成复合缓存键
          let cacheKey = cacheManager.generateCacheKeyFromURL(currentTrack.url) ?? currentTrack.id.uuidString
          cacheStatusIcon(for: cacheKey)
        }
      }
            
      HStack(spacing: 4) {
        // 播放类型指示器
        if let currentTrack = audioManager.currentTrack {
          if currentTrack.url.isFileURL && currentTrack.url.path.contains("AudioCache") {
            HStack(spacing: 4) {
              Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
                .font(.caption)
              Text("已缓存播放")
                .font(.caption)
                .foregroundColor(.green)
            }
          } else if currentTrack.url.isFileURL {
            HStack(spacing: 4) {
              Image(systemName: "folder.circle.fill")
                .foregroundColor(.blue)
                .font(.caption)
              Text("本地文件")
                .font(.caption)
                .foregroundColor(.blue)
            }
          } else {
            HStack(spacing: 4) {
              Image(systemName: "wifi.circle.fill")
                .foregroundColor(.orange)
                .font(.caption)
              Text("流媒体播放")
                .font(.caption)
                .foregroundColor(.orange)
            }
          }
        } else {
          Text("本地音乐")
            .font(.body)
            .foregroundColor(.secondary)
        }
      }
    }
  }
  
  @ViewBuilder
  private func cacheStatusIcon(for trackId: String) -> some View {
    let status = cacheManager.getCacheStatus(for: trackId)
    
    switch status {
    case .cached:
      Image(systemName: "checkmark.circle.fill")
        .foregroundColor(.green)
        .font(.caption)
    case .downloading:
      ProgressView()
        .scaleEffect(0.6)
        .frame(width: 12, height: 12)
    case .failed:
      Image(systemName: "exclamationmark.circle.fill")
        .foregroundColor(.red)
        .font(.caption)
    case .notCached:
      Image(systemName: "icloud.and.arrow.down")
        .foregroundColor(.secondary)
        .font(.caption)
    }
  }
    
  private var actionButtonsView: some View {
    let currentTrack = audioManager.currentTrack
    // 检查是否是Navidrome歌曲：有navidromeSongId 或者是 Navidrome流媒体
    let isNavidromeTrack = currentTrack != nil && (
      currentTrack!.navidromeSongId != nil || isNavidromeStream(currentTrack!)
    )
    let isButtonDisabled = isTogglingLike || !isNavidromeTrack
    
    return HStack(spacing: 40) {
      Button(action: {
        toggleLikeStatus()
      }) {
        if isTogglingLike {
          ProgressView()
            .scaleEffect(0.8)
            .frame(width: 24, height: 24)
        } else {
          Image(systemName: isLiked ? "heart.fill" : "heart")
            .font(.title2)
            .foregroundColor(isButtonDisabled ? .secondary : (isLiked ? .red : .primary))
        }
      }
      .disabled(isButtonDisabled)
            
      Spacer()
            
      Menu {
        Button(action: {
          navigateToArtist()
        }) {
          Label("跳转到艺术家", systemImage: "person.circle")
        }
        
        Button(action: {
          navigateToAlbum()
        }) {
          Label("跳转到专辑", systemImage: "opticaldisc")
        }
        
        Divider()
        
        Button(action: {
          showingPlaylistSelection = true
        }) {
          Label("添加到歌单", systemImage: "music.note.list")
        }
      } label: {
        Image(systemName: "ellipsis.circle")
          .font(.title2)
          .foregroundColor(.primary)
      }
    }
    .padding(.horizontal, 80)
  }
    
  private var playbackControlsSection: some View {
    VStack(spacing: 25) {
      progressSliderView
      playbackButtonsView
      volumeControlView
      bottomButtonsView
    }
    .padding(.bottom, 30)
  }
    
  private var progressSliderView: some View {
    VStack(spacing: 8) {
      Slider(value: Binding(
        get: {
          // 直接使用时间值，确保精确同步
          isDraggingSlider ? sliderValue : audioManager.currentTime
        },
        set: { newValue in
          sliderValue = newValue
        }
      ), in: 0...max(audioManager.duration, 0.1), onEditingChanged: { editing in
        isDraggingSlider = editing
        if !editing {
          audioManager.seek(to: sliderValue)
        }
      })
      .tint(.primary)
            
      HStack {
        Text(timeString(from: isDraggingSlider ? sliderValue : audioManager.currentTime))
          .font(.caption)
          .foregroundColor(.secondary)
                
        Spacer()
                
        Text(timeString(from: audioManager.duration))
          .font(.caption)
          .foregroundColor(.secondary)
      }
    }
    .padding(.horizontal, 20)
  }
    
  private var playbackButtonsView: some View {
    HStack(spacing: 50) {
      Button(action: { audioManager.toggleShuffle() }) {
        Image(systemName: "shuffle")
          .font(.title3)
          .foregroundColor(audioManager.isShuffleEnabled ? .blue : .primary)
      }
            
      Button(action: { audioManager.playPrevious() }) {
        Image(systemName: "backward.end.fill")
          .font(.title)
          .foregroundColor(audioManager.playlist.count > 1 ? .primary : .secondary)
      }
      .disabled(audioManager.playlist.count <= 1)
            
      playPauseButton
            
      Button(action: { audioManager.playNext() }) {
        Image(systemName: "forward.end.fill")
          .font(.title)
          .foregroundColor(audioManager.playlist.count > 1 ? .primary : .secondary)
      }
      .disabled(audioManager.playlist.count <= 1)
            
      Button(action: { audioManager.toggleRepeatMode() }) {
        Image(systemName: audioManager.repeatMode.systemImageName)
          .font(.title3)
          .foregroundColor(audioManager.repeatMode == .off ? .primary : .blue)
      }
    }
  }
    
  private var playPauseButton: some View {
    Button(action: {
      if audioManager.isPlaying {
        // 当前正在播放，暂停
        audioManager.pause()
      } else if audioManager.currentTrack != nil {
        // 有当前曲目但未播放，智能恢复播放
        audioManager.smartResume()
      } else if !audioManager.playlist.isEmpty {
        // 没有当前曲目但有播放列表，播放第一首
        audioManager.playCurrentTrack()
      }
    }) {
      Image(systemName: audioManager.isPlaying ? "pause.circle.fill" : "play.circle.fill")
        .font(.system(size: 70))
        .foregroundColor(.primary)
    }
    .disabled(audioManager.playlist.isEmpty && audioManager.currentTrack == nil)
  }
    
  private var volumeControlView: some View {
    SystemVolumeSlider(volume: Binding(
      get: { audioManager.volume },
      set: { audioManager.setSystemVolume($0) }
    ))
    .padding(.horizontal, 30)
  }
    
  private var bottomButtonsView: some View {
    HStack {
      Button(action: {
        showingPlaylist = true
      }) {
        Image(systemName: "list.bullet")
          .font(.title3)
          .foregroundColor(.primary)
      }
      
      Spacer()
      
      // 缓存管理按钮
      Button(action: {
        showingCacheManagement = true
      }) {
        ZStack {
          Image(systemName: "externaldrive")
            .font(.title3)
            .foregroundColor(.primary)
          
          // 显示下载中的数量
          if !cacheManager.downloadTasks.isEmpty {
            Circle()
              .fill(Color.red)
              .frame(width: 16, height: 16)
              .overlay(
                Text("\(cacheManager.downloadTasks.count)")
                  .font(.caption2)
                  .foregroundColor(.white)
              )
              .offset(x: 8, y: -8)
          }
        }
      }
      
      #if DEBUG
      // 调试按钮（仅在DEBUG模式显示）
      Button(action: {
        cacheManager.printCacheStatus()
        audioManager.forceRefreshNowPlayingInfo()
        print(audioManager.debugVolumeControl())
        audioManager.debugPlaybackModes()
      }) {
        Image(systemName: "ladybug")
          .font(.title3)
          .foregroundColor(.orange)
      }
      #endif
            
      Spacer()
            
      Button(action: {
        showAirPlayPicker()
      }) {
        Image(systemName: "airplayaudio")
          .font(.title3)
          .foregroundColor(.primary)
      }
    }
    .padding(.horizontal, 30)
  }
    
  // MARK: - 辅助函数
  
  /// 跳转到艺术家页面
  private func navigateToArtist() {
    guard let currentTrack = audioManager.currentTrack else {
      print("❌ 没有当前播放的歌曲")
      return
    }
    
    guard let client = navidromeClient else {
      print("❌ Navidrome客户端未初始化，尝试重新设置")
      setupNavidromeClient()
      
      // 延迟重试
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
        self.navigateToArtist()
      }
      return
    }
    
    guard client.isAuthenticated else {
      print("❌ Navidrome客户端未认证，尝试重新设置")
      setupNavidromeClient()
      
      // 延迟重试
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
        self.navigateToArtist()
      }
      return
    }
    
    // 直接从播放队列中的艺术家信息进行跳转
    let artistName: String
    if let queueArtistName = currentTrack.artistName, !queueArtistName.isEmpty {
      artistName = queueArtistName
      print("✅ 从播放队列获取艺术家名称: \(artistName)")
    } else {
      // 回退：解析艺术家名称（格式：艺术家 - 标题）
      let components = currentTrack.name.components(separatedBy: " - ")
      artistName = components.count >= 2 ? components[0] : "未知艺术家"
      print("✅ 从歌曲名称解析艺术家: \(artistName)")
    }
    
    // 通过API获取所有艺术家并搜索匹配的
    Task {
      do {
        let artists = try await client.fetchArtists()
        
        await MainActor.run {
          if let artist = artists.first(where: { $0.displayName.lowercased() == artistName.lowercased() }) ??
            artists.first(where: { $0.displayName.lowercased().contains(artistName.lowercased()) })
          {
            // 找到匹配的艺术家，直接打开详情页
            selectedArtist = artist
            showingArtistDetail = true
            print("✅ 找到艺术家: \(artist.displayName)")
          } else {
            // 没找到，回退到音乐库搜索
            navigationTargetArtist = artistName
            navigationTargetAlbum = nil
            showingMusicLibrary = true
            print("⚠️ 未找到艺术家，回退到搜索模式")
          }
        }
      } catch {
        await MainActor.run {
          // API调用失败，回退到音乐库搜索
          navigationTargetArtist = artistName
          navigationTargetAlbum = nil
          showingMusicLibrary = true
        }
      }
    }
  }
  
  /// 跳转到专辑页面
  private func navigateToAlbum() {
    guard let currentTrack = audioManager.currentTrack else { return }
    
    guard let client = navidromeClient else {
      setupNavidromeClient()
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
        self.navigateToAlbum()
      }
      return
    }
    
    guard client.isAuthenticated else {
      setupNavidromeClient()
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
        self.navigateToAlbum()
      }
      return
    }
    
    // 直接从播放队列中的专辑信息进行跳转
    if let albumId = currentTrack.navidromeAlbumId, !albumId.isEmpty {
      findAlbumById(albumId: albumId, client: client)
    } else if let albumName = currentTrack.albumName, !albumName.isEmpty {
      findAlbumByName(albumName: albumName, client: client)
    } else {
      fallbackToAlbumSearch(client: client, track: currentTrack)
    }
  }
  
  /// 通过专辑ID查找专辑
  private func findAlbumById(albumId: String, client: NavidromeAPIClient) {
    Task {
      do {
        let albums = try await client.fetchAlbums()
        
        await MainActor.run {
          if let album = albums.first(where: { $0.id == albumId }) {
            selectedAlbum = album
            showingAlbumDetail = true
          } else {
            self.fallbackToAlbumSearch(client: client, track: self.audioManager.currentTrack)
          }
        }
      } catch {
        await MainActor.run {
          self.fallbackToAlbumSearch(client: client, track: self.audioManager.currentTrack)
        }
      }
    }
  }
  
  /// 通过专辑名称查找专辑
  private func findAlbumByName(albumName: String, client: NavidromeAPIClient) {
    Task {
      do {
        let albums = try await client.fetchAlbums()
        
        await MainActor.run {
          if let album = albums.first(where: { $0.displayName.lowercased() == albumName.lowercased() }) ??
            albums.first(where: { $0.displayName.lowercased().contains(albumName.lowercased()) })
          {
            selectedAlbum = album
            showingAlbumDetail = true
          } else {
            self.fallbackToAlbumSearch(client: client, track: self.audioManager.currentTrack)
          }
        }
      } catch {
        await MainActor.run {
          self.fallbackToAlbumSearch(client: client, track: self.audioManager.currentTrack)
        }
      }
    }
  }
  
  /// 回退到音乐库搜索模式
  private func fallbackToAlbumSearch(client: NavidromeAPIClient, track: AudioFileInfo?) {
    guard let track = track else { return }
    
    let albumName = extractAlbumName(from: track)
    navigationTargetArtist = nil
    navigationTargetAlbum = albumName
    showingMusicLibrary = true
  }
  
  /// 从曲目信息中提取专辑名称
  private func extractAlbumName(from track: AudioFileInfo) -> String {
    // 如果是Navidrome流媒体，尝试从歌曲名称中解析专辑信息
    if isNavidromeStream(track) {
      // 对于Navidrome，我们需要通过API获取歌曲详细信息来获取专辑名
      // 这里先使用艺术家名作为搜索关键词
      let components = track.name.components(separatedBy: " - ")
      if components.count >= 2 {
        let artistName = components[0]
        // 返回艺术家名，这样可以搜索到该艺术家的专辑
        return artistName
      }
      return "未知专辑"
    }
    
    // 对于本地文件，使用文件夹名作为专辑名
    let folderName = track.url.deletingLastPathComponent().lastPathComponent
    return folderName.isEmpty ? "未知专辑" : folderName
  }
  
  /// 显示系统AirPlay选择器
  private func showAirPlayPicker() {
    let routePickerView = AVRoutePickerView()
    
    // 查找控制器按钮并模拟点击
    for subview in routePickerView.subviews {
      if let button = subview as? UIButton {
        button.sendActions(for: .touchUpInside)
        break
      }
    }
  }
  
  /// 从Navidrome URL中提取歌曲ID
  private func extractSongIdFromURL(_ url: URL) -> String? {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    // 查找id参数
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }
  
  private func setupNavidromeClient() {
    // 尝试从保存的配置中恢复Navidrome客户端
    let configManager = NavidromeConfigManager()
    guard let lastConfig = configManager.getLastUsedConfig() else {
      return
    }
    
    let client = NavidromeAPIClient()
    Task {
      do {
        try await client.authenticate(
          serverURL: lastConfig.serverURL,
          username: lastConfig.username,
          password: lastConfig.password
        )
        
        await MainActor.run {
          self.navidromeClient = client
          // 客户端设置成功后加载歌单列表
          self.loadAvailablePlaylists()
          // 更新当前歌曲的收藏状态
          self.updateLikeStatus(for: self.audioManager.currentTrack)
        }
      } catch {
        // 认证失败，保持静默
      }
    }
  }
    
  private func getTrackAtIndex(_ index: Int) -> AudioFileInfo? {
    guard index >= 0 && index < audioManager.playlist.count else {
      return nil
    }
    return audioManager.playlist[index]
  }
  
  private func isNavidromeStream(_ track: AudioFileInfo) -> Bool {
    // 检查URL是否包含Navidrome的特征路径
    let urlString = track.url.absoluteString
    return (urlString.contains("/rest/stream") || urlString.contains("navidrome")) &&
      (track.url.scheme == "http" || track.url.scheme == "https")
  }
  
  private func extractNavidromeCoverArtId(from track: AudioFileInfo) -> String? {
    // 从URL参数中提取歌曲ID，用作封面艺术ID
    guard let components = URLComponents(url: track.url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    // 查找id参数
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }
    
  private func timeString(from timeInterval: TimeInterval) -> String {
    let minutes = Int(timeInterval) / 60
    let seconds = Int(timeInterval) % 60
    return String(format: "%d:%02d", minutes, seconds)
  }
  
  // MARK: - 收藏功能
  
  /// 切换歌曲收藏状态
  private func toggleLikeStatus() {
    guard let track = audioManager.currentTrack else {
      print("❌ Heart button: No current track")
      return
    }
    
    // 尝试获取歌曲ID - 优先使用navidromeSongId
    let songId: String
    if let navidromeSongId = track.navidromeSongId {
      // 直接使用已有的Navidrome歌曲ID（支持缓存歌曲和流媒体歌曲）
      songId = navidromeSongId
      print("💖 使用已有的navidromeSongId切换like状态: \(songId)")
    } else if isNavidromeStream(track), let extractedId = extractSongIdFromURL(track.url) {
      // 从流媒体URL中提取ID（备用方案）
      songId = extractedId
      print("💖 从URL提取songId切换like状态: \(songId)")
    } else {
      // 对于无法获取ID的歌曲（如纯本地文件），不支持收藏功能
      print("❌ Heart button: No Navidrome song ID available for track: \(track.name)")
      return
    }
    
    guard let client = navidromeClient, client.isAuthenticated else {
      print("❌ Heart button: Navidrome client not available or authenticated")
      return
    }
    
    print("💖 Toggling like status for song: \(songId)")
    
    isTogglingLike = true
    
    Task {
      do {
        if isLiked {
          try await client.unstarSong(songId: songId)
        } else {
          try await client.starSong(songId: songId)
        }
        
        await MainActor.run {
          isLiked.toggle()
          isTogglingLike = false
          // 更新缓存中的状态
          likeStatusCache[songId] = isLiked
        }
        
      } catch {
        await MainActor.run {
          isTogglingLike = false
        }
        // 操作失败时保持静默，可以考虑显示错误提示
        print("❌ 收藏操作失败: \(error.localizedDescription)")
      }
    }
  }
  
  /// 更新当前歌曲的收藏状态
  private func updateLikeStatus(for track: AudioFileInfo?, forceRefresh: Bool = false) {
    guard let track = track,
          let client = navidromeClient,
          client.isAuthenticated
    else {
      // 对于没有客户端或未认证的情况，重置收藏状态
      isLiked = false
      return
    }
    
    // 尝试获取歌曲ID - 优先使用navidromeSongId
    let songId: String
    if let navidromeSongId = track.navidromeSongId {
      // 直接使用已有的Navidrome歌曲ID（支持缓存歌曲和流媒体歌曲）
      songId = navidromeSongId
      print("💖 使用已有的navidromeSongId获取like状态: \(songId)")
    } else if isNavidromeStream(track), let extractedId = extractSongIdFromURL(track.url) {
      // 从流媒体URL中提取ID（备用方案）
      songId = extractedId
      print("💖 从URL提取songId获取like状态: \(songId)")
    } else {
      // 对于无法获取ID的歌曲（如纯本地文件），重置收藏状态
      isLiked = false
      print("💖 无法获取songId，重置like状态")
      return
    }
    
    // 如果不强制刷新，检查缓存中是否有这首歌的收藏状态
    if !forceRefresh, let cachedStatus = likeStatusCache[songId] {
      isLiked = cachedStatus
      return
    }
    
    // 通过fetchSong获取歌曲的收藏状态
    Task {
      do {
        if let song = try await client.fetchSong(songId: songId) {
          await MainActor.run {
            let liked = song.starred != nil
            isLiked = liked
            likeStatusCache[songId] = liked
          }
        } else {
          await MainActor.run {
            isLiked = false
            likeStatusCache[songId] = false
          }
        }
        
      } catch {
        await MainActor.run {
          isLiked = false
          // 错误时不缓存，下次可以重试
        }
      }
    }
  }
}

// MARK: - Navidrome 专辑封面视图

struct NavidromeAlbumArtView: View {
  let track: AudioFileInfo
  let apiClient: NavidromeAPIClient
  let size: CGFloat
  
  @State private var coverImage: UIImage?
  @State private var isLoading: Bool = false
  @State private var coverArtId: String?
  
  var body: some View {
    Group {
      if let image = coverImage {
        Image(uiImage: image)
          .resizable()
          .aspectRatio(contentMode: .fill)
      } else if isLoading {
        ZStack {
          defaultCoverView
          ProgressView()
            .scaleEffect(1.2)
            .foregroundColor(.white)
        }
      } else {
        defaultCoverView
      }
    }
    .onAppear {
      loadCoverArt()
    }
    .onChange(of: track.id) { _, _ in
      loadCoverArt()
    }
  }
  
  private var defaultCoverView: some View {
    Rectangle()
      .fill(
        LinearGradient(
          colors: [
            Color.purple.opacity(0.6),
            Color.blue.opacity(0.4),
            Color.pink.opacity(0.3)
          ],
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
      )
      .overlay(
        VStack(spacing: 12) {
          Image(systemName: "music.note")
            .font(.system(size: size * 0.15))
            .foregroundColor(.white.opacity(0.8))
                        
          Text(track.name)
            .font(.caption)
            .foregroundColor(.white.opacity(0.9))
            .lineLimit(2)
            .multilineTextAlignment(.center)
            .padding(.horizontal, 16)
        }
      )
  }
  
  private func loadCoverArt() {
    // 重置状态
    coverImage = nil
    
    // 从URL中提取歌曲ID
    guard let songId = extractSongId(from: track.url),
          apiClient.isAuthenticated
    else {
      return
    }
    
    coverArtId = songId
    isLoading = true
    
    // 获取封面URL
    guard let coverURL = apiClient.getCoverArtURL(for: songId, size: Int(size * 2)) else {
      isLoading = false
      return
    }
    
    Task {
      do {
        let (data, response) = try await URLSession.shared.data(from: coverURL)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200,
              let image = UIImage(data: data)
        else {
          await MainActor.run {
            self.isLoading = false
          }
          return
        }
        
        await MainActor.run {
          self.coverImage = image
          self.isLoading = false
        }
        
      } catch {
        await MainActor.run {
          self.isLoading = false
        }
        // 封面加载失败，保持静默
      }
    }
  }
  
  private func extractSongId(from url: URL) -> String? {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    // 查找id参数
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }
}

// MARK: - 歌单相关视图和功能

extension MusicPlayerView {
  /// 加载可用的歌单
  private func loadAvailablePlaylists() {
    guard let client = navidromeClient else { return }
    
    Task {
      do {
        let playlists = try await client.fetchPlaylists()
        await MainActor.run {
          self.availablePlaylists = playlists
          // 歌单加载完成后，立即检查当前歌曲在各个歌单中的状态
          self.playlistStateManager.updateStatesForTrack(self.currentTrackId, with: self.navidromeClient, playlists: playlists)
        }
      } catch {
        // 加载歌单失败，保持静默
      }
    }
  }
}

// MARK: - 创建歌单视图

struct CreatePlaylistView: View {
  @Environment(\.dismiss) private var dismiss
  let apiClient: NavidromeAPIClient
  let onPlaylistCreated: () -> Void
  
  @State private var playlistName = ""
  @State private var playlistComment = ""
  @State private var isPublic = false
  @State private var isCreating = false
  @State private var showingAlert = false
  @State private var alertMessage = ""
  
  var body: some View {
    NavigationView {
      Form {
        Section(header: Text("歌单信息")) {
          TextField("歌单标题", text: $playlistName)
          TextField("描述（可选）", text: $playlistComment, axis: .vertical)
            .lineLimit(3...6)
        }
        
        Section(header: Text("设置")) {
          Toggle("公开歌单", isOn: $isPublic)
        }
      }
      .navigationTitle("创建新歌单")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarLeading) {
          Button("取消") {
            dismiss()
          }
        }
        
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("创建") {
            createPlaylist()
          }
          .disabled(playlistName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isCreating)
        }
      }
      .alert("提示", isPresented: $showingAlert) {
        Button("确定", role: .cancel) {}
      } message: {
        Text(alertMessage)
      }
    }
  }
  
  private func createPlaylist() {
    let trimmedName = playlistName.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !trimmedName.isEmpty else { return }
    
    isCreating = true
    
    Task {
      do {
        _ = try await apiClient.createPlaylist(
          name: trimmedName,
          comment: playlistComment.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : playlistComment.trimmingCharacters(in: .whitespacesAndNewlines),
          isPublic: isPublic
        )
        
        await MainActor.run {
          onPlaylistCreated()
          dismiss()
        }
        
      } catch {
        await MainActor.run {
          isCreating = false
          alertMessage = "创建歌单失败: \(error.localizedDescription)"
          showingAlert = true
        }
      }
    }
  }
}

// MARK: - 歌单状态管理器

class PlaylistStateManager: ObservableObject {
  @Published var playlistSongStates: [String: Bool] = [:]
  @Published var playlistLoadingStates: [String: Bool] = [:]
  
  private var updateTask: Task<Void, Never>?
  
  func updateStatesForTrack(_ trackId: String?, with client: NavidromeAPIClient?, playlists: [NavidromePlaylist]) {
    guard let trackId = trackId, let client = client else { return }
    
    // 取消之前的任务
    updateTask?.cancel()
    
    updateTask = Task { @MainActor in
      // 添加小延迟避免频繁调用
      try? await Task.sleep(nanoseconds: 300_000_000) // 0.3秒
      
      if !Task.isCancelled {
        await updatePlaylistStates(trackId: trackId, client: client, playlists: playlists)
      }
    }
  }
  
  @MainActor
  private func updatePlaylistStates(trackId: String, client: NavidromeAPIClient, playlists: [NavidromePlaylist]) async {
    var newStates: [String: Bool] = [:]
    
    // 并发检查所有歌单状态
    await withTaskGroup(of: (String, Bool).self) { group in
      for playlist in playlists {
        group.addTask {
          do {
            let isInPlaylist = try await client.isSongInPlaylist(playlistId: playlist.id, songId: trackId)
            return (playlist.id, isInPlaylist)
          } catch {
            // 检查歌单状态失败，保持静默
            return (playlist.id, false)
          }
        }
      }
      
      for await (playlistId, isInPlaylist) in group {
        newStates[playlistId] = isInPlaylist
      }
    }
    
    // 一次性更新所有状态
    playlistSongStates = newStates
  }
  
  func toggleSongInPlaylist(_ playlist: NavidromePlaylist, trackId: String?, client: NavidromeAPIClient?) async {
    guard let trackId = trackId, let client = client else { return }
    
    await MainActor.run {
      playlistLoadingStates[playlist.id] = true
    }
    
    do {
      let isInPlaylist = try await client.isSongInPlaylist(playlistId: playlist.id, songId: trackId)
      
      if isInPlaylist {
        let songs = try await client.fetchPlaylistSongs(playlistId: playlist.id)
        if let index = songs.firstIndex(where: { $0.id == trackId }) {
          try await client.removeSongFromPlaylist(playlistId: playlist.id, songIndex: index)
        }
      } else {
        try await client.addSongToPlaylist(playlistId: playlist.id, songId: trackId)
      }
      
      await MainActor.run {
        playlistLoadingStates[playlist.id] = false
        playlistSongStates[playlist.id] = !isInPlaylist
      }
      
    } catch {
      await MainActor.run {
        playlistLoadingStates[playlist.id] = false
        // 歌单操作失败，保持静默
      }
    }
  }
}

// MARK: - 歌单选择页面

struct PlaylistSelectionView: View {
  let apiClient: NavidromeAPIClient
  let currentTrack: AudioFileInfo?
  @Binding var availablePlaylists: [NavidromePlaylist]
  @ObservedObject var playlistStateManager: PlaylistStateManager
  let onCreatePlaylist: () -> Void
  let onPlaylistsUpdated: () -> Void
  
  @Environment(\.dismiss) private var dismiss
  @State private var searchText = ""
  
  var filteredPlaylists: [NavidromePlaylist] {
    if searchText.isEmpty {
      return availablePlaylists
    } else {
      return availablePlaylists.filter { playlist in
        playlist.displayName.localizedCaseInsensitiveContains(searchText)
      }
    }
  }
  
  var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        // 当前歌曲信息
        if let track = currentTrack {
          VStack(spacing: 12) {
            HStack(spacing: 12) {
              // 歌曲封面缩略图
              if !track.url.isFileURL {
                NavidromeAlbumArtView(
                  track: track,
                  apiClient: apiClient,
                  size: 50
                )
                .frame(width: 50, height: 50)
                .cornerRadius(8)
              } else {
                RoundedRectangle(cornerRadius: 8)
                  .fill(Color.gray.opacity(0.3))
                  .frame(width: 50, height: 50)
                  .overlay(
                    Image(systemName: "music.note")
                      .foregroundColor(.gray)
                  )
              }
              
              VStack(alignment: .leading, spacing: 4) {
                Text(track.name)
                  .font(.headline)
                  .lineLimit(1)
                
                if let artistName = track.artistName {
                  Text(artistName)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                }
              }
              
              Spacer()
            }
            .padding(.horizontal)
            .padding(.top)
            
            Divider()
          }
        }
        
        // 搜索栏
        HStack {
          Image(systemName: "magnifyingglass")
            .foregroundColor(.secondary)
          
          TextField("搜索歌单", text: $searchText)
            .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        
        // 歌单列表
        List {
          ForEach(filteredPlaylists) { playlist in
            PlaylistRowItemView(
              playlist: playlist,
              currentTrack: currentTrack,
              playlistStateManager: playlistStateManager,
              apiClient: apiClient
            )
          }
          
          // 创建新歌单按钮
          Button(action: onCreatePlaylist) {
            HStack {
              Image(systemName: "plus.circle.fill")
                .foregroundColor(.blue)
              
              Text("创建新歌单")
                .foregroundColor(.blue)
              
              Spacer()
            }
            .padding(.vertical, 8)
          }
        }
        .listStyle(PlainListStyle())
      }
      .navigationTitle("添加到歌单")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("完成") {
            dismiss()
          }
        }
      }
    }
    .onAppear {
      // 页面出现时更新歌单状态
      if let trackId = currentTrack?.navidromeSongId {
        playlistStateManager.updateStatesForTrack(trackId, with: apiClient, playlists: availablePlaylists)
      }
    }
  }
}

// MARK: - 歌单行项目视图

struct PlaylistRowItemView: View {
  let playlist: NavidromePlaylist
  let currentTrack: AudioFileInfo?
  @ObservedObject var playlistStateManager: PlaylistStateManager
  let apiClient: NavidromeAPIClient
  
  private var isInPlaylist: Bool? {
    playlistStateManager.playlistSongStates[playlist.id]
  }
  
  private var isLoading: Bool {
    playlistStateManager.playlistLoadingStates[playlist.id] == true
  }
  
  var body: some View {
    Button(action: {
      togglePlaylistState()
    }) {
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text(playlist.displayName)
            .font(.headline)
            .foregroundColor(.primary)
            .lineLimit(1)
          
          if let comment = playlist.comment, !comment.isEmpty {
            Text(comment)
              .font(.caption)
              .foregroundColor(.secondary)
              .lineLimit(2)
          }
          
          if playlist.isPublic == true {
            Text("公开歌单")
              .font(.caption2)
              .foregroundColor(.secondary)
          }
        }
        
        Spacer()
        
        // 状态指示器
        if isLoading {
          ProgressView()
            .scaleEffect(0.8)
        } else if let inPlaylist = isInPlaylist {
          Image(systemName: inPlaylist ? "checkmark.circle.fill" : "plus.circle")
            .font(.title2)
            .foregroundColor(inPlaylist ? .green : .blue)
        } else {
          Image(systemName: "questionmark.circle")
            .font(.title2)
            .foregroundColor(.secondary)
        }
      }
      .padding(.vertical, 4)
    }
    .buttonStyle(PlainButtonStyle())
  }
  
  private func togglePlaylistState() {
    guard let trackId = currentTrack?.navidromeSongId else { return }
    
    Task {
      await playlistStateManager.toggleSongInPlaylist(playlist, trackId: trackId, client: apiClient)
    }
  }
}

#Preview {
  MusicPlayerView()
}
