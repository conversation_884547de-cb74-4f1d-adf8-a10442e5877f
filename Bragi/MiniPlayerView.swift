//
//  MiniPlayerView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import SwiftUI

struct MiniPlayerView: View {
  @ObservedObject var audioManager = AudioPlayerManager.shared
  @State private var navidromeClient: NavidromeAPIClient?
    
  var body: some View {
    VStack(spacing: 0) {
      // 进度条
      ProgressView(value: audioManager.duration > 0 ? audioManager.currentTime / audioManager.duration : 0)
        .progressViewStyle(LinearProgressViewStyle())
        .tint(.primary)
        .scaleEffect(y: 0.5)
            
      // 播放器内容
      HStack(spacing: 12) {
        // 专辑封面
        albumCoverView
                
        // 歌曲信息
        VStack(alignment: .leading, spacing: 2) {
          Text(audioManager.currentTrack?.name ?? "夜空中最亮的星")
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.primary)
            .lineLimit(1)
                    
          HStack(spacing: 4) {
            // 播放类型指示器
            if let currentTrack = audioManager.currentTrack {
              if currentTrack.url.isFileURL && currentTrack.url.path.contains("AudioCache") {
                Image(systemName: "checkmark.circle.fill")
                  .foregroundColor(.green)
                  .font(.caption2)
                Text("已缓存")
                  .font(.caption)
                  .foregroundColor(.green)
              } else if currentTrack.url.isFileURL {
                Image(systemName: "folder.circle.fill")
                  .foregroundColor(.blue)
                  .font(.caption2)
                Text("本地文件")
                  .font(.caption)
                  .foregroundColor(.blue)
              } else {
                Image(systemName: "wifi.circle.fill")
                  .foregroundColor(.orange)
                  .font(.caption2)
                Text("流媒体")
                  .font(.caption)
                  .foregroundColor(.orange)
              }
            } else {
              Text("本地音乐")
                .font(.caption)
                .foregroundColor(.secondary)
            }
          }
          .lineLimit(1)
        }
                
        Spacer()
                
        // 控制按钮
        HStack(spacing: 20) {
          Button(action: { audioManager.playPrevious() }) {
            Image(systemName: "backward.fill")
              .font(.title3)
              .foregroundColor(audioManager.playlist.count > 1 ? .primary : .secondary)
          }
          .disabled(audioManager.playlist.count <= 1)
                    
          Button(action: {
            if audioManager.isPlaying {
              audioManager.pause()
            } else if audioManager.currentTrack != nil {
              audioManager.smartResume()
            } else if !audioManager.playlist.isEmpty {
              audioManager.playCurrentTrack()
            }
          }) {
            Image(systemName: audioManager.isPlaying ? "pause.fill" : "play.fill")
              .font(.title2)
              .foregroundColor(.primary)
          }
          .disabled(audioManager.playlist.isEmpty && audioManager.currentTrack == nil)
                    
          Button(action: { audioManager.playNext() }) {
            Image(systemName: "forward.fill")
              .font(.title3)
              .foregroundColor(audioManager.playlist.count > 1 ? .primary : .secondary)
          }
          .disabled(audioManager.playlist.count <= 1)
        }
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 8)
    }
    .background(
      Rectangle()
        .fill(.ultraThinMaterial)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -2)
    )
    .onAppear {
      setupNavidromeClient()
    }
  }
  
  private var albumCoverView: some View {
    Group {
      if let track = audioManager.currentTrack, isNavidromeStream(track), let client = navidromeClient {
        NavidromeMiniAlbumArtView(
          track: track,
          apiClient: client,
          size: 50
        )
      } else if let track = audioManager.currentTrack, let artwork = audioManager.getArtworkForTrack(track) {
        Image(uiImage: artwork)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 50, height: 50)
          .cornerRadius(8)
      } else {
        // 默认封面
        RoundedRectangle(cornerRadius: 8)
          .fill(
            LinearGradient(
              gradient: Gradient(colors: [
                Color.purple.opacity(0.8),
                Color.blue.opacity(0.6)
              ]),
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 50, height: 50)
          .overlay(
            Image(systemName: "music.note")
              .font(.title3)
              .foregroundColor(.white.opacity(0.8))
          )
      }
    }
  }
  
  // MARK: - 辅助函数
  
  private func setupNavidromeClient() {
    let configManager = NavidromeConfigManager()
    guard let lastConfig = configManager.getLastUsedConfig() else {
      return
    }
    
    let client = NavidromeAPIClient()
    Task {
      do {
        try await client.authenticate(
          serverURL: lastConfig.serverURL,
          username: lastConfig.username,
          password: lastConfig.password
        )
        
        await MainActor.run {
          self.navidromeClient = client
        }
      } catch {
        print("迷你播放器Navidrome认证失败: \(error.localizedDescription)")
      }
    }
  }
  
  private func isNavidromeStream(_ track: AudioFileInfo) -> Bool {
    let urlString = track.url.absoluteString
    return (urlString.contains("/rest/stream") || urlString.contains("navidrome")) &&
      (track.url.scheme == "http" || track.url.scheme == "https")
  }
}

// MARK: - Navidrome 迷你专辑封面视图

struct NavidromeMiniAlbumArtView: View {
  let track: AudioFileInfo
  let apiClient: NavidromeAPIClient
  let size: CGFloat
  
  @State private var coverImage: UIImage?
  @State private var isLoading: Bool = false
  
  var body: some View {
    Group {
      if let image = coverImage {
        Image(uiImage: image)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: size, height: size)
          .cornerRadius(8)
      } else if isLoading {
        ZStack {
          defaultCoverView
          ProgressView()
            .scaleEffect(0.8)
            .foregroundColor(.white)
        }
      } else {
        defaultCoverView
      }
    }
    .onAppear {
      loadCoverArt()
    }
    .onChange(of: track.id) { _, _ in
      loadCoverArt()
    }
  }
  
  private var defaultCoverView: some View {
    RoundedRectangle(cornerRadius: 8)
      .fill(
        LinearGradient(
          gradient: Gradient(colors: [
            Color.purple.opacity(0.8),
            Color.blue.opacity(0.6)
          ]),
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
      )
      .frame(width: size, height: size)
      .overlay(
        Image(systemName: "music.note")
          .font(.title3)
          .foregroundColor(.white.opacity(0.8))
      )
  }
  
  private func loadCoverArt() {
    coverImage = nil
    
    guard let songId = extractSongId(from: track.url),
          apiClient.isAuthenticated
    else {
      return
    }
    
    isLoading = true
    
    guard let coverURL = apiClient.getCoverArtURL(for: songId, size: Int(size * 2)) else {
      isLoading = false
      return
    }
    
    Task {
      do {
        let (data, response) = try await URLSession.shared.data(from: coverURL)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200,
              let image = UIImage(data: data)
        else {
          await MainActor.run {
            self.isLoading = false
          }
          return
        }
        
        await MainActor.run {
          self.coverImage = image
          self.isLoading = false
        }
        
      } catch {
        await MainActor.run {
          self.isLoading = false
        }
        print("加载Navidrome迷你封面失败: \(error.localizedDescription)")
      }
    }
  }
  
  private func extractSongId(from url: URL) -> String? {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }
}

#Preview {
  VStack {
    Spacer()
    MiniPlayerView()
  }
  .background(Color.gray.opacity(0.1))
}
