//
//  CacheManagementView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import SwiftUI

struct CacheManagementView: View {
  @ObservedObject private var cacheManager = AudioCacheManager.shared
  @Environment(\.dismiss) private var dismiss
  @State private var showingClearConfirmation = false
  @State private var selectedCacheSizeIndex = 0
  
  var body: some View {
    NavigationView {
      List {
        // 缓存大小设置部分
        Section("缓存大小限制") {
          cacheSizeSettingSection
        }
        
        // 缓存概览部分
        Section("缓存概览") {
          cacheOverviewSection
        }
        
        // 下载任务部分
        if !cacheManager.downloadTasks.isEmpty {
          Section("正在下载") {
            ForEach(Array(cacheManager.downloadTasks.keys), id: \.self) { trackId in
              if let task = cacheManager.downloadTasks[trackId] {
                DownloadTaskRow(task: task)
              }
            }
          }
        }
        
        // 操作部分
        Section("缓存管理") {
          cacheManagementSection
        }
      }
      .navigationTitle("缓存管理")
      .navigationBarTitleDisplayMode(.large)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("完成") {
            dismiss()
          }
        }
      }
      .alert("清理所有缓存", isPresented: $showingClearConfirmation) {
        Button("取消", role: .cancel) {}
        Button("清理", role: .destructive) {
          cacheManager.clearAllCache()
        }
      } message: {
        Text("这将删除所有已缓存的音频文件，此操作无法撤销。")
      }
    }
    .onAppear {
      // 初始化选中的缓存大小索引
      initializeCacheSizeSelection()
    }
  }
  
  private var cacheSizeSettingSection: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Image(systemName: "externaldrive.badge.gearshape")
          .foregroundColor(.purple)
          .font(.title2)
        
        VStack(alignment: .leading, spacing: 4) {
          Text("缓存大小限制")
            .font(.headline)
          Text("选择最大缓存空间")
            .font(.caption)
            .foregroundColor(.secondary)
        }
        
        Spacer()
      }
      
      // 缓存大小选择器
      Picker("缓存大小", selection: $selectedCacheSizeIndex) {
        ForEach(0 ..< AudioCacheManager.getCacheSizeOptionTexts().count, id: \.self) { index in
          Text(AudioCacheManager.getCacheSizeOptionTexts()[index])
            .tag(index)
        }
      }
      .pickerStyle(SegmentedPickerStyle())
      .onChange(of: selectedCacheSizeIndex) { _, newIndex in
        let newSize = AudioCacheManager.getCacheSizeOptions()[newIndex]
        cacheManager.setCacheSizeLimit(newSize)
      }
    }
    .padding(.vertical, 8)
  }
  
  private var cacheOverviewSection: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Image(systemName: "externaldrive.fill")
          .foregroundColor(.blue)
          .font(.title2)
        
        VStack(alignment: .leading, spacing: 4) {
          Text("缓存大小")
            .font(.headline)
          Text(formatBytes(cacheManager.cacheSize))
            .font(.subheadline)
            .foregroundColor(.secondary)
        }
        
        Spacer()
        
        VStack(alignment: .trailing, spacing: 4) {
          Text("\(formatBytes(cacheManager.maxCacheSize)) 限制")
            .font(.caption)
            .foregroundColor(.secondary)
          
          // 缓存使用进度条
          ProgressView(value: Double(cacheManager.cacheSize), total: Double(cacheManager.maxCacheSize))
            .frame(width: 80)
            .tint(cacheUsageColor)
        }
      }
      
      // 缓存状态统计
      HStack(spacing: 20) {
        cacheStatItem(
          icon: "checkmark.circle.fill",
          color: .green,
          count: cacheManager.cacheStatus.values.filter { $0 == .cached }.count,
          label: "已缓存"
        )
        
        cacheStatItem(
          icon: "arrow.down.circle",
          color: .blue,
          count: cacheManager.cacheStatus.values.filter { $0 == .downloading }.count,
          label: "下载中"
        )
        
        cacheStatItem(
          icon: "exclamationmark.circle.fill",
          color: .red,
          count: cacheManager.cacheStatus.values.filter { $0 == .failed }.count,
          label: "失败"
        )
      }
    }
    .padding(.vertical, 8)
  }
  
  private var cacheManagementSection: some View {
    Group {
      Button(action: {
        Task {
          await cacheManager.cleanExpiredCache()
        }
      }) {
        Label("清理过期缓存", systemImage: "clock.arrow.circlepath")
      }
      
      Button(action: {
        Task {
          await cacheManager.manageCacheSize()
        }
      }) {
        Label("优化缓存空间", systemImage: "arrow.up.arrow.down.circle")
      }
      
      Button(action: {
        showingClearConfirmation = true
      }) {
        Label("清理所有缓存", systemImage: "trash")
          .foregroundColor(.red)
      }
    }
  }
  
  private var cacheUsageColor: Color {
    let usage = Double(cacheManager.cacheSize) / Double(cacheManager.maxCacheSize)
    if usage > 0.9 {
      return .red
    } else if usage > 0.7 {
      return .orange
    } else {
      return .green
    }
  }
  
  private func initializeCacheSizeSelection() {
    let currentSize = cacheManager.maxCacheSize
    let options = AudioCacheManager.getCacheSizeOptions()
    
    // 找到当前缓存大小对应的索引
    if let index = options.firstIndex(of: currentSize) {
      selectedCacheSizeIndex = index
    } else {
      // 如果当前大小不在选项中，选择最接近的
      selectedCacheSizeIndex = 0 // 默认选择1GB
      for (index, option) in options.enumerated() {
        if currentSize <= option {
          selectedCacheSizeIndex = index
          break
        }
      }
    }
  }
  
  private func cacheStatItem(icon: String, color: Color, count: Int, label: String) -> some View {
    VStack(spacing: 4) {
      HStack(spacing: 4) {
        Image(systemName: icon)
          .foregroundColor(color)
          .font(.caption)
        Text("\(count)")
          .font(.caption)
          .fontWeight(.semibold)
      }
      
      Text(label)
        .font(.caption2)
        .foregroundColor(.secondary)
    }
  }
  
  private func formatBytes(_ bytes: Int64) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useGB, .useMB, .useKB]
    formatter.countStyle = .binary
    return formatter.string(fromByteCount: bytes)
  }
}

// MARK: - 下载任务行

struct DownloadTaskRow: View {
  @ObservedObject var task: AudioDownloadTask
  
  var body: some View {
    HStack {
      VStack(alignment: .leading, spacing: 4) {
        Text("下载中...")
          .font(.headline)
        
        Text(task.trackId)
          .font(.caption)
          .foregroundColor(.secondary)
          .lineLimit(1)
      }
      
      Spacer()
      
      VStack(alignment: .trailing, spacing: 4) {
        ProgressView(value: task.progress)
          .frame(width: 60)
        
        if task.totalSize > 0 {
          Text("\(formatBytes(task.downloadedSize)) / \(formatBytes(task.totalSize))")
            .font(.caption2)
            .foregroundColor(.secondary)
        }
      }
    }
    .padding(.vertical, 4)
  }
  
  private func formatBytes(_ bytes: Int64) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB]
    formatter.countStyle = .binary
    return formatter.string(fromByteCount: bytes)
  }
}

#Preview {
  CacheManagementView()
}
