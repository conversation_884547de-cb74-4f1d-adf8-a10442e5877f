//
//  NavidromeModels.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import Foundation

// MARK: - Navidrome 数据结构

// Navidrome 播放列表信息
struct NavidromePlaylist: Codable, Identifiable, Equatable {
  let id: String
  let name: String
  let comment: String?
  let owner: String?
  let isPublic: Bool?
  let songCount: Int
  let duration: Int // 秒
  let created: String
  let changed: String?
  let coverArt: String?
  
  var displayName: String {
    return name.isEmpty ? "未知播放列表" : name
  }
  
  var durationString: String {
    let minutes = duration / 60
    let seconds = duration % 60
    return String(format: "%d:%02d", minutes, seconds)
  }
  
  var createdString: String {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
    if let date = formatter.date(from: created) {
      let displayFormatter = DateFormatter()
      displayFormatter.dateStyle = .medium
      displayFormatter.timeStyle = .short
      return displayFormatter.string(from: date)
    }
    return created
  }
  
  static func == (lhs: NavidromePlaylist, rhs: NavidromePlaylist) -> Bool {
    lhs.id == rhs.id
  }
}

// Navidrome 专辑信息
struct NavidromeAlbum: Codable, Identifiable, Equatable {
  let id: String
  let name: String
  let artist: String?
  let artistId: String?
  let coverArt: String?
  let songCount: Int
  let duration: Int // 秒
  let playCount: Int?
  let created: String
  let year: Int?
  let genre: String?
  
  var displayName: String {
    return name.isEmpty ? "未知专辑" : name
  }
  
  var displayArtist: String {
    return artist?.isEmpty == false ? artist! : "未知艺术家"
  }
  
  var durationString: String {
    let minutes = duration / 60
    let seconds = duration % 60
    return String(format: "%d:%02d", minutes, seconds)
  }
  
  var yearString: String {
    if let year = year {
      return String(year)
    }
    return ""
  }
  
  static func == (lhs: NavidromeAlbum, rhs: NavidromeAlbum) -> Bool {
    lhs.id == rhs.id
  }
}

// Navidrome 艺术家信息
struct NavidromeArtist: Codable, Identifiable, Equatable {
  let id: String
  let name: String
  let coverArt: String?
  let albumCount: Int
  let starred: String?
  
  var displayName: String {
    return name.isEmpty ? "未知艺术家" : name
  }
  
  var albumCountString: String {
    return "\(albumCount) 张专辑"
  }
  
  static func == (lhs: NavidromeArtist, rhs: NavidromeArtist) -> Bool {
    lhs.id == rhs.id
  }
}

// Navidrome 歌曲信息
struct NavidromeSong: Codable, Identifiable, Equatable {
  let id: String
  let title: String
  let artist: String
  let album: String
  let track: Int?
  let year: Int?
  let genre: String?
  let duration: Int // 秒
  let size: Int // 文件大小（字节）
  let suffix: String // 文件扩展名
  let albumId: String
  let artistId: String
  let path: String
  let createdAt: String
  let updatedAt: String
  let coverArt: String? // 封面艺术ID
  let starred: String? // 收藏状态
  
  // 计算属性
  var displayTitle: String {
    return title.isEmpty ? "未知标题" : title
  }
  
  var displayArtist: String {
    return artist.isEmpty ? "未知艺术家" : artist
  }
  
  var displayAlbum: String {
    return album.isEmpty ? "未知专辑" : album
  }
  
  var durationString: String {
    let minutes = duration / 60
    let seconds = duration % 60
    return String(format: "%d:%02d", minutes, seconds)
  }
  
  var fileSizeString: String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB]
    formatter.countStyle = .file
    return formatter.string(fromByteCount: Int64(size))
  }
  
  var dateAddedString: String {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
    if let date = formatter.date(from: createdAt) {
      let displayFormatter = DateFormatter()
      displayFormatter.dateStyle = .medium
      displayFormatter.timeStyle = .short
      return displayFormatter.string(from: date)
    }
    return createdAt
  }
  
  static func == (lhs: NavidromeSong, rhs: NavidromeSong) -> Bool {
    lhs.id == rhs.id
  }
}

// Navidrome 连接配置
struct NavidromeConfig: Codable {
  let serverURL: String
  let username: String
  let password: String
  let lastConnected: Date
  
  var displayName: String {
    return "\(username)@\(URL(string: serverURL)?.host ?? serverURL)"
  }
}

// Navidrome API 错误
enum NavidromeError: Error, LocalizedError {
  case invalidServerURL
  case authenticationFailed
  case networkError(String)
  case invalidResponse
  case tokenExpired
  
  var errorDescription: String? {
    switch self {
    case .invalidServerURL:
      return "无效的服务器地址"
    case .authenticationFailed:
      return "认证失败，请检查用户名和密码"
    case .networkError(let message):
      return "网络错误: \(message)"
    case .invalidResponse:
      return "服务器响应格式错误"
    case .tokenExpired:
      return "登录已过期，请重新登录"
    }
  }
}
