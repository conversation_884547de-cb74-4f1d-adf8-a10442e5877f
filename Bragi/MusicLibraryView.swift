//
//  MusicLibraryView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import Foundation
import Kingfisher
import SwiftUI

// MARK: - 音乐库分类枚举

enum MusicLibraryCategory: String, CaseIterable {
  case favorites = "Favorites"
  case playlists = "Playlists"
  case albums = "Albums"
  case artists = "Artists"
  case songs = "Songs"
  
  var displayName: String {
    switch self {
    case .favorites: return "我喜欢"
    case .playlists: return "播放列表"
    case .albums: return "专辑"
    case .artists: return "艺术家"
    case .songs: return "歌曲"
    }
  }
  
  var icon: String {
    switch self {
    case .favorites: return "heart.fill"
    case .playlists: return "music.note.list"
    case .albums: return "square.stack"
    case .artists: return "person.circle"
    case .songs: return "music.note"
    }
  }
}

// MARK: - 主视图

struct MusicLibraryView: View {
  @Environment(\.dismiss) private var dismiss
  @StateObject private var apiClient = NavidromeAPIClient()
  @StateObject private var configManager = NavidromeConfigManager()
  @ObservedObject private var audioManager = AudioPlayerManager.shared
  @StateObject private var cacheManager = AudioCacheManager.shared
  @StateObject private var libraryCacheManager = MusicLibraryCacheManager.shared
  @StateObject private var favoriteManager = FavoriteStatusManager.shared
    
  @State private var songs: [NavidromeSong] = []
  @State private var favoriteSongs: [NavidromeSong] = []
  @State private var playlists: [NavidromePlaylist] = []
  @State private var albums: [NavidromeAlbum] = []
  @State private var artists: [NavidromeArtist] = []
  @State private var selectedCategory: MusicLibraryCategory = .songs
  @State private var isLoading = false
  @State private var isRefreshing = false
  @State private var showingLoginView = false
  @State private var showingAlert = false
  @State private var alertMessage = ""
  @State private var searchText = ""
  @State private var isAutoLoggingIn = false
  @State private var hasTriedAutoLogin = false
  @State private var showingNetworkSettings = false
  
  // 缓存相关状态
  @State private var lastLoadedFromCache = false
  @State private var cacheStatusText = ""
  
  // 导航相关参数
  private let initialCategory: MusicLibraryCategory?
  private let targetArtistName: String?
  private let targetAlbumName: String?
  
  // 初始化方法
  init(
    initialCategory: MusicLibraryCategory? = nil,
    targetArtistName: String? = nil,
    targetAlbumName: String? = nil
  ) {
    self.initialCategory = initialCategory
    self.targetArtistName = targetArtistName
    self.targetAlbumName = targetAlbumName
  }
  
  var filteredSongs: [NavidromeSong] {
    let songsToFilter = songs.sorted { $0.id < $1.id } // 确保基础数据按ID排序
    
    if searchText.isEmpty {
      return songsToFilter
    } else {
      return songsToFilter.filter { song in
        song.displayTitle.localizedCaseInsensitiveContains(searchText) ||
          song.displayArtist.localizedCaseInsensitiveContains(searchText) ||
          song.displayAlbum.localizedCaseInsensitiveContains(searchText)
      } // 过滤后的结果已经是排序的，因为基础数据已排序
    }
  }
  
  var filteredFavoriteSongs: [NavidromeSong] {
    let songsToFilter = favoriteSongs.sorted { $0.id < $1.id } // 确保基础数据按ID排序
    
    if searchText.isEmpty {
      return songsToFilter
    } else {
      return songsToFilter.filter { song in
        song.displayTitle.localizedCaseInsensitiveContains(searchText) ||
          song.displayArtist.localizedCaseInsensitiveContains(searchText) ||
          song.displayAlbum.localizedCaseInsensitiveContains(searchText)
      } // 过滤后的结果已经是排序的，因为基础数据已排序
    }
  }
  
  var filteredPlaylists: [NavidromePlaylist] {
    if searchText.isEmpty {
      return playlists
    } else {
      return playlists.filter { playlist in
        playlist.displayName.localizedCaseInsensitiveContains(searchText) ||
          (playlist.owner?.localizedCaseInsensitiveContains(searchText) ?? false)
      }
    }
  }
  
  var filteredAlbums: [NavidromeAlbum] {
    if searchText.isEmpty {
      return albums
    } else {
      return albums.filter { album in
        album.displayName.localizedCaseInsensitiveContains(searchText) ||
          album.displayArtist.localizedCaseInsensitiveContains(searchText) ||
          (album.genre?.localizedCaseInsensitiveContains(searchText) ?? false)
      }
    }
  }
  
  var filteredArtists: [NavidromeArtist] {
    if searchText.isEmpty {
      return artists
    } else {
      return artists.filter { artist in
        artist.displayName.localizedCaseInsensitiveContains(searchText)
      }
    }
  }
    
  var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        if isAutoLoggingIn {
          // 自动登录中状态
          autoLoginView
        } else if !apiClient.isAuthenticated {
          // 未登录状态
          loginPromptView
        } else {
          // 分类选择器
          categorySegmentedControl
          
          if isLoading {
            // 加载中状态
            loadingView
          } else {
            // 内容视图
            contentView
          }
        }
      }
      .navigationTitle("Navidrome 音乐库")
      .navigationBarTitleDisplayMode(.large)
      .toolbar {
        ToolbarItem(placement: .navigationBarLeading) {
          Button("返回") { dismiss() }
        }
        
        ToolbarItem(placement: .navigationBarTrailing) {
          HStack {
            if apiClient.isAuthenticated {
              // 缓存状态指示器
              if isRefreshing {
                ProgressView()
                  .scaleEffect(0.8)
                  .frame(width: 20, height: 20)
              }
              
              Button(action: { showingNetworkSettings = true }) {
                Image(systemName: "gear")
                  .font(.title2)
              }
              
              Button(action: refreshContent) {
                Image(systemName: "arrow.clockwise")
                  .font(.title2)
              }
              .disabled(isLoading || isRefreshing)
              
              Button(action: {
                apiClient.logout()
                songs = []
                playlists = []
                albums = []
                artists = []
                hasTriedAutoLogin = false
                // 清理当前数据但保留缓存
              }) {
                Image(systemName: "person.crop.circle.badge.xmark")
                  .font(.title2)
              }
            } else {
              Button(action: { showingLoginView = true }) {
                Image(systemName: "person.crop.circle.badge.plus")
                  .font(.title2)
              }
            }
          }
        }
      }
    }
    .searchable(text: $searchText, prompt: searchPrompt)
    .sheet(isPresented: $showingLoginView) {
      NavidromeLoginView(
        apiClient: apiClient,
        configManager: configManager
      ) {
        hasTriedAutoLogin = true
        Task {
          await loadContent()
        }
      }
    }
    .sheet(isPresented: $showingNetworkSettings) {
      NetworkSettingsView()
    }
    .alert("提示", isPresented: $showingAlert) {
      Button("确定", role: .cancel) {}
    } message: {
      Text(alertMessage)
    }
    .onAppear {
      // 设置初始分类
      if let initialCategory = initialCategory {
        selectedCategory = initialCategory
      }
      
      if !hasTriedAutoLogin {
        hasTriedAutoLogin = true
        Task {
          await attemptAutoLogin()
        }
      } else if apiClient.isAuthenticated {
        // 已登录状态，检查是否需要加载内容
        if shouldLoadContent() {
          Task {
            await loadContent()
            await handleNavigationTarget()
          }
        } else {
          Task {
            await handleNavigationTarget()
          }
        }
      }
    }
  }
  
  // MARK: - 计算属性
  
  private var searchPrompt: String {
    switch selectedCategory {
    case .favorites: return "搜索我喜欢的歌曲"
    case .playlists: return "搜索播放列表"
    case .albums: return "搜索专辑"
    case .artists: return "搜索艺术家"
    case .songs: return "搜索歌曲、艺术家或专辑"
    }
  }
  
  // MARK: - 视图组件
  
  private var categorySegmentedControl: some View {
    VStack(spacing: 0) {
      Picker("分类", selection: $selectedCategory) {
        ForEach(MusicLibraryCategory.allCases, id: \.self) { category in
          Text(category.displayName)
            .tag(category)
        }
      }
      .pickerStyle(SegmentedPickerStyle())
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
      
      // 缓存状态显示
      if !cacheStatusText.isEmpty {
        HStack {
          Image(systemName: lastLoadedFromCache ? "checkmark.circle.fill" : "network")
            .foregroundColor(lastLoadedFromCache ? .green : .blue)
            .font(.caption)
          
          Text(cacheStatusText)
            .font(.caption)
            .foregroundColor(.secondary)
          
          Spacer()
          
          // 固定宽度的进度指示器容器，避免高度变化
          HStack {
            if isRefreshing {
              ProgressView()
                .scaleEffect(0.6)
            } else {
              // 占位符，保持固定宽度
              Color.clear
                .frame(width: 16, height: 16)
            }
          }
          .frame(width: 20, height: 20) // 固定容器大小
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8) // 增加垂直内边距，确保固定高度
        .frame(minHeight: 32) // 设置最小高度
      }
      
      Divider()
    }
    .background(Color(.systemBackground))
    .onChange(of: selectedCategory) { _, _ in
      Task {
        await loadContent()
      }
    }
  }
  
  private var contentView: some View {
    Group {
      switch selectedCategory {
      case .favorites:
        if favoriteSongs.isEmpty {
          emptyStateView
        } else {
          favoritesView
        }
      case .songs:
        if songs.isEmpty {
          emptyStateView
        } else {
          songListView
        }
      case .playlists:
        if playlists.isEmpty {
          emptyStateView
        } else {
          playlistsView
        }
      case .albums:
        if albums.isEmpty {
          emptyStateView
        } else {
          albumsView
        }
      case .artists:
        if artists.isEmpty {
          emptyStateView
        } else {
          artistsView
        }
      }
    }
    .onAppear {
      print("📱 ContentView显示 - 分类: \(selectedCategory.displayName)")
      print("📱 数据状态: 歌曲\(songs.count)首, 播放列表\(playlists.count)个, 专辑\(albums.count)个, 艺术家\(artists.count)个")
    }
  }
  
  private var playlistsView: some View {
    List {
      ForEach(filteredPlaylists) { playlist in
        NavigationLink(destination: PlaylistDetailView(playlist: playlist, apiClient: apiClient, onDismissLibrary: {
          dismiss()
        })) {
          NavidromePlaylistRow(playlist: playlist, apiClient: apiClient) {
            // 点击行为由NavigationLink处理
          }
        }
        .buttonStyle(PlainButtonStyle())
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadContent()
    }
  }
  
  private var albumsView: some View {
    List {
      ForEach(filteredAlbums) { album in
        NavigationLink(destination: AlbumDetailView(album: album, apiClient: apiClient, onDismissLibrary: {
          dismiss()
        })) {
          NavidromeAlbumRow(album: album, apiClient: apiClient) {
            // 点击行为由NavigationLink处理
          }
        }
        .buttonStyle(PlainButtonStyle())
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadContent()
    }
  }
  
  private var artistsView: some View {
    List {
      ForEach(filteredArtists) { artist in
        NavigationLink(destination: ArtistDetailView(artist: artist, apiClient: apiClient)) {
          NavidromeArtistRow(artist: artist) {
            // 点击行为由NavigationLink处理
          }
        }
        .buttonStyle(PlainButtonStyle())
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadContent()
    }
  }
  
  private var favoritesView: some View {
    List {
      // 播放全部按钮
      if !filteredFavoriteSongs.isEmpty {
        Button(action: {
          Task {
            await playAllFavorites()
          }
        }) {
          HStack {
            Image(systemName: "play.fill")
              .foregroundColor(.white)
              .frame(width: 30, height: 30)
              .background(Color.red)
              .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 2) {
              Text("播放全部")
                .font(.headline)
                .foregroundColor(.primary)
              
              Text("\(filteredFavoriteSongs.count) 首我喜欢的歌曲")
                .font(.caption)
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
              .foregroundColor(.secondary)
              .font(.caption)
          }
          .padding(.vertical, 8)
          .padding(.horizontal, 16)
          .contentShape(Rectangle()) // 确保整个区域都可以点击
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color.clear) // 添加透明背景确保点击区域
      }
      
      // 收藏歌曲列表
      ForEach(filteredFavoriteSongs, id: \.id) { song in
        NavidromeSongRow(
          song: song,
          isCurrentTrack: isCurrentTrack(song),
          isPlaying: audioManager.isPlaying && isCurrentTrack(song),
          apiClient: apiClient,
          cacheManager: cacheManager
        ) {
          Task {
            await playSong(song)
          }
        }
        .contextMenu {
          Button(action: {
            Task { await playSong(song) }
          }) {
            Label("播放", systemImage: "play.fill")
          }
          
          Button(action: {
            Task { await addSongToPlaylist(song) }
          }) {
            Label("添加到播放列表", systemImage: "plus")
          }
          
          Button(action: {
            Task { await unstarSong(song) }
          }) {
            Label("取消收藏", systemImage: "heart.slash")
          }
        }
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadContent()
    }
  }
  
  private var autoLoginView: some View {
    VStack(spacing: 30) {
      VStack(spacing: 16) {
        Image(systemName: "music.note.house")
          .font(.system(size: 60))
          .foregroundColor(.purple)
        
        Text("正在连接 Navidrome")
          .font(.title)
          .fontWeight(.bold)
          .foregroundColor(.primary)
        
        Text("正在尝试使用保存的账号自动登录...")
          .font(.body)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
          .padding(.horizontal)
      }
      
      ProgressView()
        .scaleEffect(1.5)
        .foregroundColor(.purple)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var loginPromptView: some View {
    VStack(spacing: 30) {
      VStack(spacing: 16) {
        Image(systemName: "music.note.house")
          .font(.system(size: 60))
          .foregroundColor(.purple)
        
        Text("连接到 Navidrome")
          .font(.title)
          .fontWeight(.bold)
          .foregroundColor(.primary)
        
        Text("请登录您的 Navidrome 服务器来访问您的音乐库")
          .font(.body)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
          .padding(.horizontal)
      }
      
      Button(action: { showingLoginView = true }) {
        HStack {
          Image(systemName: "person.crop.circle.badge.plus")
          Text("登录")
        }
        .foregroundColor(.white)
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .background(
          LinearGradient(
            gradient: Gradient(colors: [Color.purple, Color.blue]),
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(25)
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var loadingView: some View {
    VStack(spacing: 20) {
      ProgressView()
        .scaleEffect(1.5)
      
      Text("正在加载音乐...")
        .font(.headline)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var emptyStateView: some View {
    VStack(spacing: 20) {
      Image(systemName: "music.note.list")
        .font(.system(size: 60))
        .foregroundColor(.secondary)
      
      Text("暂无音乐")
        .font(.title2)
        .fontWeight(.medium)
        .foregroundColor(.primary)
      
      Text("您的音乐库中暂时没有歌曲")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      VStack(spacing: 12) {
        Button(action: refreshContent) {
          HStack {
            Image(systemName: "arrow.clockwise")
            Text("刷新")
          }
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 12)
          .background(
            LinearGradient(
              gradient: Gradient(colors: [Color.purple, Color.blue]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(25)
        }
        
        Button(action: {
          Task {
            await testAPIConnection()
          }
        }) {
          HStack {
            Image(systemName: "network")
            Text("测试连接")
          }
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 12)
          .background(
            LinearGradient(
              gradient: Gradient(colors: [Color.green, Color.teal]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(25)
        }
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var songListView: some View {
    List {
      // 歌曲列表
      ForEach(filteredSongs, id: \.id) { song in
        NavidromeSongRow(
          song: song,
          isCurrentTrack: isCurrentTrack(song),
          isPlaying: audioManager.isPlaying && isCurrentTrack(song),
          apiClient: apiClient,
          cacheManager: cacheManager
        ) {
          Task {
            await playSong(song)
          }
        }
        .contextMenu {
          Button(action: {
            Task { await playSong(song) }
          }) {
            Label("播放", systemImage: "play.fill")
          }

          Button(action: {
            Task { await addSongToPlaylist(song) }
          }) {
            Label("添加到播放列表", systemImage: "plus")
          }

          // 收藏/取消收藏按钮
          if favoriteManager.isFavorite(songId: song.id) {
            Button(action: {
              Task { await unstarSong(song) }
            }) {
              Label("取消收藏", systemImage: "heart.slash")
            }
          } else {
            Button(action: {
              Task { await starSong(song) }
            }) {
              Label("收藏", systemImage: "heart")
            }
          }
        }
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadContent()
    }
  }
  
  // MARK: - 辅助方法
  
  private func isCurrentTrack(_ song: NavidromeSong) -> Bool {
    guard let currentTrack = audioManager.currentTrack else { return false }
    
    // 使用统一的显示名称格式进行比较
    let songDisplayName = createDisplayName(for: song)
    return currentTrack.name == songDisplayName
  }
  
  private func createDisplayName(for song: NavidromeSong) -> String {
    return "\(song.displayArtist) - \(song.displayTitle)"
  }
  
  // MARK: - 功能方法
  
  private func attemptAutoLogin() async {
    // 检查是否有保存的配置
    guard let lastConfig = configManager.getLastUsedConfig() else {
      // 没有保存的配置，直接显示登录页面
      await MainActor.run {
        showingLoginView = true
      }
      return
    }
    
    await MainActor.run {
      isAutoLoggingIn = true
    }
    
    do {
      // 尝试使用保存的配置自动登录
      try await apiClient.authenticate(
        serverURL: lastConfig.serverURL,
        username: lastConfig.username,
        password: lastConfig.password
      )
      
      await MainActor.run {
        isAutoLoggingIn = false
        // 更新配置的最后连接时间
        let updatedConfig = NavidromeConfig(
          serverURL: lastConfig.serverURL,
          username: lastConfig.username,
          password: lastConfig.password,
          lastConnected: Date()
        )
        configManager.saveConfig(updatedConfig)
      }
      
      // 自动登录成功，测试API连接
      print("📡 自动登录成功，开始测试API连接")
      await testAPIConnection()
      
      // 加载内容
      print("📡 开始加载内容")
      await loadContent()
      
      // 处理导航目标
      await handleNavigationTarget()
      
    } catch {
      await MainActor.run {
        isAutoLoggingIn = false
        // 自动登录失败，显示登录页面
        showingLoginView = true
        
        // 可选：显示自动登录失败的提示
        if case NavidromeError.authenticationFailed = error {
          alertMessage = "自动登录失败，请重新输入密码"
        } else {
          alertMessage = "自动登录失败: \(error.localizedDescription)"
        }
        showingAlert = true
      }
    }
  }
  
  private func loadContent() async {
    guard let config = apiClient.currentConfig else {
      print("📦 无法加载内容：缺少API配置")
      return
    }
    
    let serverConfigId = libraryCacheManager.generateServerConfigId(
      serverURL: config.serverURL,
      username: config.username
    )
    
    // 第一步：尝试从缓存加载
    await loadFromCache(serverConfigId: serverConfigId)
    
    // 第二步：从网络获取最新数据
    await loadFromNetwork(serverConfigId: serverConfigId)
  }
  
  private func loadFromCache(serverConfigId: String) async {
    await MainActor.run {
      isLoading = true
      lastLoadedFromCache = false
      cacheStatusText = "正在加载缓存..."
    }
    
    var loadedFromCache = false
    
    switch selectedCategory {
    case .favorites:
      // 收藏歌曲不使用缓存，总是从网络获取最新数据
      break
    case .songs:
      if let cachedSongs = libraryCacheManager.getCachedSongs(for: serverConfigId) {
        await MainActor.run {
          // 确保缓存的歌曲也按ID排序
          self.songs = cachedSongs.sorted { $0.id < $1.id }
          loadedFromCache = true
          print("📦 从缓存加载歌曲: \(cachedSongs.count) 首，已按ID排序")
        }
      }
    case .playlists:
      if let cachedPlaylists = libraryCacheManager.getCachedPlaylists(for: serverConfigId) {
        await MainActor.run {
          self.playlists = cachedPlaylists
          loadedFromCache = true
          print("📦 从缓存加载播放列表: \(cachedPlaylists.count) 个")
        }
      }
    case .albums:
      if let cachedAlbums = libraryCacheManager.getCachedAlbums(for: serverConfigId) {
        await MainActor.run {
          self.albums = cachedAlbums
          loadedFromCache = true
          print("📦 从缓存加载专辑: \(cachedAlbums.count) 个")
        }
      }
    case .artists:
      if let cachedArtists = libraryCacheManager.getCachedArtists(for: serverConfigId) {
        await MainActor.run {
          self.artists = cachedArtists
          loadedFromCache = true
          print("📦 从缓存加载艺术家: \(cachedArtists.count) 个")
        }
      }
    }
    
    await MainActor.run {
      self.lastLoadedFromCache = loadedFromCache
      if loadedFromCache {
        self.isLoading = false
        self.cacheStatusText = "已从缓存加载，正在刷新..."
      }
    }
  }
  
  private func loadFromNetwork(serverConfigId: String) async {
    await MainActor.run {
      if !lastLoadedFromCache {
        isLoading = true
      }
      isRefreshing = true
      cacheStatusText = lastLoadedFromCache ? "正在刷新数据..." : "正在从网络加载..."
    }
    
    print("📡 开始从网络加载数据，分类: \(selectedCategory.displayName)")
    
    do {
      switch selectedCategory {
      case .favorites:
        print("📡 开始获取收藏歌曲数据...")
        let fetchedFavorites = try await apiClient.fetchStarredSongs()
        print("📡 成功获取收藏歌曲数据: \(fetchedFavorites.count) 首")
        await handleFavoritesUpdate(fetchedFavorites)
        
      case .songs:
        print("📡 开始获取歌曲数据...")
        let fetchedSongs = try await apiClient.fetchSongs(limit: 500)
        print("📡 成功获取歌曲数据: \(fetchedSongs.count) 首")
        await handleSongsUpdate(fetchedSongs, serverConfigId: serverConfigId)
        
      case .playlists:
        print("📡 开始获取播放列表数据...")
        let fetchedPlaylists = try await apiClient.fetchPlaylists()
        print("📡 成功获取播放列表数据: \(fetchedPlaylists.count) 个")
        await handlePlaylistsUpdate(fetchedPlaylists, serverConfigId: serverConfigId)
        
      case .albums:
        print("📡 开始获取专辑数据...")
        let fetchedAlbums = try await apiClient.fetchAlbums(limit: 500)
        print("📡 成功获取专辑数据: \(fetchedAlbums.count) 个")
        await handleAlbumsUpdate(fetchedAlbums, serverConfigId: serverConfigId)
        
      case .artists:
        print("📡 开始获取艺术家数据...")
        let fetchedArtists = try await apiClient.fetchArtists()
        print("📡 成功获取艺术家数据: \(fetchedArtists.count) 个")
        await handleArtistsUpdate(fetchedArtists, serverConfigId: serverConfigId)
      }
      
      await MainActor.run {
        self.isLoading = false
        self.isRefreshing = false
        self.cacheStatusText = "数据已更新"
      }
      
    } catch {
      await MainActor.run {
        self.isLoading = false
        self.isRefreshing = false
        let categoryName = selectedCategory.displayName
        
        if self.lastLoadedFromCache {
          // 如果有缓存数据，网络请求失败时不显示错误
          self.cacheStatusText = "网络更新失败，显示缓存数据"
          print("📦 网络更新失败但有缓存数据: \(error.localizedDescription)")
        } else {
          // 没有缓存数据时才显示错误
          self.alertMessage = "加载\(categoryName)失败: \(error.localizedDescription)"
          self.showingAlert = true
          self.cacheStatusText = "加载失败"
        }
      }
    }
  }
  
  // MARK: - 数据更新处理
  
  private func handleSongsUpdate(_ newSongs: [NavidromeSong], serverConfigId: String) async {
    let cachedSongs = songs // 当前显示的数据
    let shouldUpdate = libraryCacheManager.shouldUpdateSongs(newSongs, cachedSongs: cachedSongs)

    print("📦 处理歌曲更新: 新数据\(newSongs.count)首, 缓存数据\(cachedSongs.count)首, 需要更新: \(shouldUpdate)")

    // 总是保存新数据到缓存
    libraryCacheManager.saveSongs(newSongs, for: serverConfigId)

    if shouldUpdate || !lastLoadedFromCache {
      await MainActor.run {
        // 确保更新的歌曲也按ID排序
        self.songs = newSongs.sorted { $0.id < $1.id }
        // 更新全局收藏状态管理器
        favoriteManager.updateFavoriteStatuses(from: newSongs)
        print("📦 歌曲数据已更新: \(self.songs.count) 首，已按ID排序")
      }
    } else {
      print("📦 歌曲数据无变化，保持当前显示")
    }
  }
  
  private func handlePlaylistsUpdate(_ newPlaylists: [NavidromePlaylist], serverConfigId: String) async {
    let cachedPlaylists = playlists
    let shouldUpdate = libraryCacheManager.shouldUpdatePlaylists(newPlaylists, cachedPlaylists: cachedPlaylists)
    
    print("📦 处理播放列表更新: 新数据\(newPlaylists.count)个, 缓存数据\(cachedPlaylists.count)个, 需要更新: \(shouldUpdate)")
    
    libraryCacheManager.savePlaylists(newPlaylists, for: serverConfigId)
    
    if shouldUpdate || !lastLoadedFromCache {
      await MainActor.run {
        self.playlists = newPlaylists
        print("📦 播放列表数据已更新: \(self.playlists.count) 个")
      }
    } else {
      print("📦 播放列表数据无变化，保持当前显示")
    }
  }
  
  private func handleAlbumsUpdate(_ newAlbums: [NavidromeAlbum], serverConfigId: String) async {
    let cachedAlbums = albums
    let shouldUpdate = libraryCacheManager.shouldUpdateAlbums(newAlbums, cachedAlbums: cachedAlbums)
    
    libraryCacheManager.saveAlbums(newAlbums, for: serverConfigId)
    
    if shouldUpdate || !lastLoadedFromCache {
      await MainActor.run {
        self.albums = newAlbums
        print("📦 专辑数据已更新: \(newAlbums.count) 个")
      }
    } else {
      print("📦 专辑数据无变化，保持当前显示")
    }
  }
  
  private func handleArtistsUpdate(_ newArtists: [NavidromeArtist], serverConfigId: String) async {
    let cachedArtists = artists
    let shouldUpdate = libraryCacheManager.shouldUpdateArtists(newArtists, cachedArtists: cachedArtists)
    
    libraryCacheManager.saveArtists(newArtists, for: serverConfigId)
    
    if shouldUpdate || !lastLoadedFromCache {
      await MainActor.run {
        self.artists = newArtists
        print("📦 艺术家数据已更新: \(newArtists.count) 个")
      }
    } else {
      print("📦 艺术家数据无变化，保持当前显示")
    }
  }
  
  private func handleFavoritesUpdate(_ newFavorites: [NavidromeSong]) async {
    await MainActor.run {
      // 收藏歌曲按ID排序
      self.favoriteSongs = newFavorites.sorted { $0.id < $1.id }
      // 更新全局收藏状态管理器
      favoriteManager.updateFavoriteStatuses(from: newFavorites)
      print("📦 收藏歌曲数据已更新: \(self.favoriteSongs.count) 首，已按ID排序")
    }
  }
  
  private func refreshContent() {
    Task {
      await loadContent()
    }
  }
  
  /// 测试API连接和数据获取
  private func testAPIConnection() async {
    print("🧪 开始测试API连接...")
    
    guard apiClient.isAuthenticated else {
      print("🧪 测试失败：用户未认证")
      return
    }
    
    do {
      // 测试获取播放列表
      print("🧪 测试获取播放列表...")
      let testPlaylists = try await apiClient.fetchPlaylists()
      print("🧪 成功获取播放列表: \(testPlaylists.count) 个")
      
      // 测试获取专辑
      print("🧪 测试获取专辑...")
      let testAlbums = try await apiClient.fetchAlbums(limit: 5)
      print("🧪 成功获取专辑: \(testAlbums.count) 个")
      
      if !testAlbums.isEmpty {
        let firstAlbum = testAlbums[0]
        print("🧪 第一个专辑: \(firstAlbum.displayName) - \(firstAlbum.displayArtist)")
        
        // 从第一个专辑获取歌曲
        print("🧪 测试从专辑获取歌曲...")
        let albumSongs = try await apiClient.fetchAlbumSongs(albumId: firstAlbum.id)
        print("🧪 专辑歌曲数量: \(albumSongs.count) 首")
        
        if !albumSongs.isEmpty {
          let firstSong = albumSongs[0]
          print("🧪 第一首歌曲: \(firstSong.displayTitle) - \(firstSong.displayArtist)")
        }
      }
      
      // 测试获取歌曲（使用getRandomSongs）
      print("🧪 测试获取随机歌曲...")
      let testSongs = try await apiClient.fetchSongs(limit: 10)
      print("🧪 成功获取随机歌曲: \(testSongs.count) 首")
      
    } catch {
      print("🧪 API测试失败: \(error.localizedDescription)")
    }
  }
  
  /// 判断是否需要加载内容（当前分类没有数据时）
  private func shouldLoadContent() -> Bool {
    let result: Bool
    switch selectedCategory {
    case .favorites:
      result = favoriteSongs.isEmpty
    case .songs:
      result = songs.isEmpty
    case .playlists:
      result = playlists.isEmpty
    case .albums:
      result = albums.isEmpty
    case .artists:
      result = artists.isEmpty
    }
    
    print("📦 shouldLoadContent检查: 分类=\(selectedCategory.displayName), 需要加载=\(result)")
    print("📦 当前数据量: 收藏\(favoriteSongs.count), 歌曲\(songs.count), 播放列表\(playlists.count), 专辑\(albums.count), 艺术家\(artists.count)")
    
    return result
  }
  
  /// 处理导航目标（跳转到指定艺术家或专辑）
  private func handleNavigationTarget() async {
    // 处理艺术家导航
    if let targetArtistName = targetArtistName {
      await MainActor.run {
        selectedCategory = .artists
        searchText = targetArtistName
      }
      print("🎯 导航到艺术家: \(targetArtistName)")
    }
    
    // 处理专辑导航
    if let targetAlbumName = targetAlbumName {
      await MainActor.run {
        selectedCategory = .albums
        searchText = targetAlbumName
      }
      print("🎯 导航到专辑: \(targetAlbumName)")
    }
  }
  
  private func playSong(_ song: NavidromeSong) async {
    guard let streamURL = apiClient.getStreamURL(for: song) else {
      await MainActor.run {
        alertMessage = "无法获取播放链接"
        showingAlert = true
      }
      return
    }
    
    await MainActor.run {
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      print("🎵 音乐库播放歌曲: \(song.displayTitle)")
      print("🔗 Navidrome ID: \(song.id)")
      print("🆔 生成的UUID: \(audioFileInfo.id.uuidString)")
      print("🌐 播放URL: \(streamURL.absoluteString)")
      AudioPlayerManager.shared.playAudio(file: audioFileInfo)
    }
  }
  
  private func addSongToPlaylist(_ song: NavidromeSong) async {
    guard let streamURL = apiClient.getStreamURL(for: song) else {
      await MainActor.run {
        alertMessage = "无法获取播放链接"
        showingAlert = true
      }
      return
    }
    
    await MainActor.run {
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      AudioPlayerManager.shared.addToPlaylist(audioFileInfo)
    }
  }
  
  private func playAllSongs(_ songs: [NavidromeSong]) async {
    guard !songs.isEmpty else {
      await MainActor.run {
        alertMessage = "没有歌曲可播放"
        showingAlert = true
      }
      return
    }
    
    // 将所有歌曲转换为AudioFileInfo
    var audioFiles: [AudioFileInfo] = []
    
    for song in songs {
      guard let streamURL = apiClient.getStreamURL(for: song) else {
        continue
      }
      
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      audioFiles.append(audioFileInfo)
    }
    
    await MainActor.run {
      if !audioFiles.isEmpty {
        AudioPlayerManager.shared.replacePlaylist(with: audioFiles, startFromIndex: 0)
        print("🎵 音乐库播放列表已替换: \(audioFiles.count)首歌曲")
      } else {
        alertMessage = "无法获取任何播放链接"
        showingAlert = true
      }
    }
  }
  
  private func playAllFavorites() async {
    await playAllSongs(filteredFavoriteSongs)
    // 播放开始后关闭整个音乐库页面，返回到播放页面
    await MainActor.run {
      dismiss()
    }
  }
  
  private func starSong(_ song: NavidromeSong) async {
    do {
      try await apiClient.starSong(songId: song.id)

      // 更新歌曲的收藏状态
      await MainActor.run {
        // 更新全局收藏状态管理器
        favoriteManager.updateFavoriteStatus(songId: song.id, isFavorite: true)
        // 更新当前歌曲列表中的收藏状态
        updateSongStarredStatus(songId: song.id, isStarred: true)
        print("💖 已收藏歌曲: \(song.displayTitle)")
      }

    } catch {
      await MainActor.run {
        alertMessage = "收藏失败: \(error.localizedDescription)"
        showingAlert = true
      }
    }
  }

  private func unstarSong(_ song: NavidromeSong) async {
    do {
      try await apiClient.unstarSong(songId: song.id)

      // 更新歌曲的收藏状态
      await MainActor.run {
        // 更新全局收藏状态管理器
        favoriteManager.updateFavoriteStatus(songId: song.id, isFavorite: false)
        // 从收藏列表中移除该歌曲
        favoriteSongs.removeAll { $0.id == song.id }
        // 更新当前歌曲列表中的收藏状态
        updateSongStarredStatus(songId: song.id, isStarred: false)
        print("💔 已取消收藏歌曲: \(song.displayTitle)")
      }

    } catch {
      await MainActor.run {
        alertMessage = "取消收藏失败: \(error.localizedDescription)"
        showingAlert = true
      }
    }
  }

  /// 更新歌曲的收藏状态
  private func updateSongStarredStatus(songId: String, isStarred: Bool) {
    let starredValue = isStarred ? Date().ISO8601Format() : nil

    // 更新歌曲列表中的收藏状态
    for i in 0..<songs.count {
      if songs[i].id == songId {
        songs[i] = NavidromeSong(
          id: songs[i].id,
          title: songs[i].title,
          artist: songs[i].artist,
          album: songs[i].album,
          track: songs[i].track,
          year: songs[i].year,
          genre: songs[i].genre,
          duration: songs[i].duration,
          size: songs[i].size,
          suffix: songs[i].suffix,
          albumId: songs[i].albumId,
          artistId: songs[i].artistId,
          path: songs[i].path,
          createdAt: songs[i].createdAt,
          updatedAt: songs[i].updatedAt,
          coverArt: songs[i].coverArt,
          starred: starredValue
        )
        break
      }
    }

    // 更新收藏歌曲列表中的收藏状态
    for i in 0..<favoriteSongs.count {
      if favoriteSongs[i].id == songId {
        favoriteSongs[i] = NavidromeSong(
          id: favoriteSongs[i].id,
          title: favoriteSongs[i].title,
          artist: favoriteSongs[i].artist,
          album: favoriteSongs[i].album,
          track: favoriteSongs[i].track,
          year: favoriteSongs[i].year,
          genre: favoriteSongs[i].genre,
          duration: favoriteSongs[i].duration,
          size: favoriteSongs[i].size,
          suffix: favoriteSongs[i].suffix,
          albumId: favoriteSongs[i].albumId,
          artistId: favoriteSongs[i].artistId,
          path: favoriteSongs[i].path,
          createdAt: favoriteSongs[i].createdAt,
          updatedAt: favoriteSongs[i].updatedAt,
          coverArt: favoriteSongs[i].coverArt,
          starred: starredValue
        )
        break
      }
    }
  }

  private func createAudioFileInfo(from song: NavidromeSong, streamURL: URL) -> AudioFileInfo {
    // 直接使用song.id作为UUID，如果无法转换则生成随机UUID
    // 缓存系统现在直接使用song.id作为key，所以AudioFileInfo的UUID不重要
    let uuid: UUID
    if let existingUUID = UUID(uuidString: song.id) {
      uuid = existingUUID
    } else {
      uuid = UUID()
    }
    
    // 使用统一的显示名称格式
    let displayName = createDisplayName(for: song)
    
    // 处理空字符串问题
    let albumId = song.albumId.isEmpty ? nil : song.albumId
    let artistId = song.artistId.isEmpty ? nil : song.artistId
    
    // 调试输出
    print("🎵 创建AudioFileInfo调试:")
    print("  歌曲: \(song.displayTitle)")
    print("  原始albumId: '\(song.albumId)'")
    print("  处理后albumId: \(albumId ?? "nil")")
    print("  专辑名: \(song.displayAlbum)")
    print("  艺术家名: \(song.displayArtist)")
    
    return AudioFileInfo(
      id: uuid,
      name: displayName,
      fileName: "\(song.displayTitle).\(song.suffix)",
      url: streamURL,
      fileSize: song.size,
      dateAdded: Date(),
      duration: Double(song.duration), // 将Navidrome的时长（秒）转换为Double
      navidromeSongId: song.id,
      navidromeAlbumId: albumId,
      navidromeArtistId: artistId,
      albumName: song.displayAlbum,
      artistName: song.displayArtist
    )
  }
}

// MARK: - 保存的配置卡片视图

struct SavedConfigCard: View {
  let config: NavidromeConfig
  let onSelect: () -> Void
  let onDelete: () -> Void
  
  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        VStack(alignment: .leading, spacing: 4) {
          Text(config.username)
            .font(.headline)
            .foregroundColor(.primary)
            .lineLimit(1)
          
          Text(URL(string: config.serverURL)?.host ?? config.serverURL)
            .font(.caption)
            .foregroundColor(.secondary)
            .lineLimit(1)
          
          Text("最后连接: \(formatDate(config.lastConnected))")
            .font(.caption2)
            .foregroundColor(.secondary)
        }
        
        Spacer()
        
        Button(action: onDelete) {
          Image(systemName: "xmark.circle.fill")
            .font(.caption)
            .foregroundColor(.red)
        }
      }
      
      Button(action: onSelect) {
        HStack {
          Image(systemName: "arrow.right.circle.fill")
          Text("使用此配置")
        }
        .font(.caption)
        .foregroundColor(.blue)
      }
    }
    .padding(12)
    .background(Color(.systemGray6))
    .cornerRadius(12)
    .frame(width: 200)
  }
  
  private func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .short
    return formatter.string(from: date)
  }
}

// MARK: - 封面图片处理

// 封面图片缓存相关代码已删除

// MARK: - 封面图片组件

struct CoverArtView: View {
  let coverArtId: String?
  let apiClient: NavidromeAPIClient
  let size: CGFloat
  let cornerRadius: CGFloat
  let showProgress: Bool
  
  @State private var coverImage: UIImage?
  @State private var isLoading: Bool = false
  @State private var loadFailed: Bool = false
  
  init(coverArtId: String?, apiClient: NavidromeAPIClient, size: CGFloat = 50, cornerRadius: CGFloat = 8, showProgress: Bool = false) {
    self.coverArtId = coverArtId
    self.apiClient = apiClient
    self.size = size
    self.cornerRadius = cornerRadius
    self.showProgress = showProgress
  }
  
  var body: some View {
    Group {
      if let image = coverImage {
        Image(uiImage: image)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: size, height: size)
      } else if isLoading && showProgress {
        ZStack {
          defaultCoverView
          ProgressView()
            .scaleEffect(0.8)
            .foregroundColor(.white)
        }
      } else {
        defaultCoverView
      }
    }
    .frame(width: size, height: size)
    .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
    .onAppear {
      loadCoverArt()
    }
    .onChange(of: coverArtId) { _, _ in
      loadCoverArt()
    }
  }
  
  private var defaultCoverView: some View {
    RoundedRectangle(cornerRadius: cornerRadius)
      .fill(
        LinearGradient(
          gradient: Gradient(colors: [Color.purple.opacity(0.7), Color.blue.opacity(0.5)]),
          startPoint: .topLeading,
          endPoint: .bottomTrailing
        )
      )
      .overlay(
        Image(systemName: "music.note")
          .font(.system(size: size * 0.4))
          .foregroundColor(.white)
      )
      .frame(width: size, height: size)
  }
  
  private func loadCoverArt() {
    // 重置状态
    coverImage = nil
    loadFailed = false
    
    guard let coverArtId = coverArtId,
          !coverArtId.isEmpty,
          let coverURL = apiClient.getCoverArtURL(for: coverArtId, size: Int(size * 2))
    else {
      return
    }
    
    isLoading = true
    
    Task {
      do {
        let (data, response) = try await URLSession.shared.data(from: coverURL)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200,
              let image = UIImage(data: data)
        else {
          await MainActor.run {
            self.isLoading = false
            self.loadFailed = true
          }
          return
        }
        
        await MainActor.run {
          self.coverImage = image
          self.isLoading = false
        }
        
      } catch {
        await MainActor.run {
          self.isLoading = false
          self.loadFailed = true
        }
        print("加载封面失败: \(error.localizedDescription)")
      }
    }
  }
}

// MARK: - 播放列表行视图

struct NavidromePlaylistRow: View {
  let playlist: NavidromePlaylist
  let apiClient: NavidromeAPIClient
  let onTap: () -> Void
  
  var body: some View {
    HStack(spacing: 12) {
      // 播放列表封面或图标
      if let coverArt = playlist.coverArt {
        CoverArtView(
          coverArtId: coverArt,
          apiClient: apiClient,
          size: 40,
          cornerRadius: 8
        )
      } else {
        Image(systemName: "music.note.list")
          .font(.title2)
          .foregroundColor(.purple)
          .frame(width: 40, height: 40)
          .background(Color.purple.opacity(0.1))
          .cornerRadius(8)
      }
      
      // 播放列表信息
      VStack(alignment: .leading, spacing: 4) {
        Text(playlist.displayName)
          .font(.headline)
          .foregroundColor(.primary)
          .lineLimit(1)
        
        HStack {
          Text("\(playlist.songCount) 首歌曲")
            .font(.caption)
            .foregroundColor(.secondary)
          
          if !playlist.durationString.isEmpty {
            Text("•")
              .font(.caption)
              .foregroundColor(.secondary)
            
            Text(playlist.durationString)
              .font(.caption)
              .foregroundColor(.secondary)
          }
        }
        
        if let owner = playlist.owner {
          Text("创建者: \(owner)")
            .font(.caption2)
            .foregroundColor(.secondary)
        }
      }
      
      Spacer()
    }
    .padding(.vertical, 8)
  }
}

// MARK: - 专辑行视图

struct NavidromeAlbumRow: View {
  let album: NavidromeAlbum
  let apiClient: NavidromeAPIClient
  let onTap: () -> Void
  
  var body: some View {
    HStack(spacing: 12) {
      // 专辑封面
      CoverArtView(
        coverArtId: album.coverArt,
        apiClient: apiClient,
        size: 50,
        cornerRadius: 8,
        showProgress: true
      )
      
      // 专辑信息
      VStack(alignment: .leading, spacing: 4) {
        Text(album.displayName)
          .font(.headline)
          .foregroundColor(.primary)
          .lineLimit(1)
        
        Text(album.displayArtist)
          .font(.subheadline)
          .foregroundColor(.secondary)
          .lineLimit(1)
        
        HStack {
          if !album.yearString.isEmpty {
            Text(album.yearString)
              .font(.caption)
              .foregroundColor(.secondary)
            
            Text("•")
              .font(.caption)
              .foregroundColor(.secondary)
          }
          
          Text("\(album.songCount) 首歌曲")
            .font(.caption)
            .foregroundColor(.secondary)
          
          Text("•")
            .font(.caption)
            .foregroundColor(.secondary)
          
          Text(album.durationString)
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
      
      Spacer()
    }
    .padding(.vertical, 8)
  }
}

// MARK: - 艺术家行视图

struct NavidromeArtistRow: View {
  let artist: NavidromeArtist
  let onTap: () -> Void
  
  var body: some View {
    HStack(spacing: 12) {
      // 艺术家头像占位符
      Circle()
        .fill(Color.blue.opacity(0.1))
        .frame(width: 50, height: 50)
        .overlay(
          Image(systemName: "person.circle")
            .font(.title)
            .foregroundColor(.blue)
        )
      
      // 艺术家信息
      VStack(alignment: .leading, spacing: 4) {
        Text(artist.displayName)
          .font(.headline)
          .foregroundColor(.primary)
          .lineLimit(1)
        
        Text(artist.albumCountString)
          .font(.subheadline)
          .foregroundColor(.secondary)
      }
      
      Spacer()
    }
    .padding(.vertical, 8)
  }
}

#Preview {
  MusicLibraryView()
}
