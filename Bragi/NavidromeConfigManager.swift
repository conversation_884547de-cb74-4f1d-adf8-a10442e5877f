//
//  NavidromeConfigManager.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import Foundation

// MARK: - Navidrome 配置管理

class NavidromeConfigManager: ObservableObject {
  @Published var savedConfigs: [NavidromeConfig] = []
  
  private let configsFileName = "navidrome_configs.json"
  
  init() {
    loadConfigs()
  }
  
  func saveConfig(_ config: NavidromeConfig) {
    // 移除相同的配置（如果存在）
    savedConfigs.removeAll { savedConfig in
      savedConfig.serverURL == config.serverURL && savedConfig.username == config.username
    }
    
    // 添加新配置到开头
    savedConfigs.insert(config, at: 0)
    
    // 最多保存5个配置
    if savedConfigs.count > 5 {
      savedConfigs = Array(savedConfigs.prefix(5))
    }
    
    saveConfigs()
  }
  
  func deleteConfig(at index: Int) {
    guard index < savedConfigs.count else { return }
    savedConfigs.remove(at: index)
    saveConfigs()
  }
  
  func getLastUsedConfig() -> NavidromeConfig? {
    return savedConfigs.first
  }
  
  private func loadConfigs() {
    do {
      let documentsDirectory = FileManager.default.urls(for: .documentDirectory,
                                                        in: .userDomainMask).first!
      let configsFile = documentsDirectory.appendingPathComponent(configsFileName)
      
      if FileManager.default.fileExists(atPath: configsFile.path) {
        let data = try Data(contentsOf: configsFile)
        savedConfigs = try JSONDecoder().decode([NavidromeConfig].self, from: data)
        
        // 按最后连接时间排序
        savedConfigs.sort { $0.lastConnected > $1.lastConnected }
      }
    } catch {
      print("加载Navidrome配置失败: \(error.localizedDescription)")
    }
  }
  
  private func saveConfigs() {
    do {
      let documentsDirectory = FileManager.default.urls(for: .documentDirectory,
                                                        in: .userDomainMask).first!
      let configsFile = documentsDirectory.appendingPathComponent(configsFileName)
      
      let data = try JSONEncoder().encode(savedConfigs)
      try data.write(to: configsFile)
    } catch {
      print("保存Navidrome配置失败: \(error.localizedDescription)")
    }
  }
}
