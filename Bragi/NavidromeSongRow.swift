//
//  NavidromeSongRow.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import SwiftUI

// MARK: - Navidrome 歌曲行视图

struct NavidromeSongRow: View {
  let song: NavidromeSong
  let isCurrentTrack: Bool
  let isPlaying: Bool
  let apiClient: NavidromeAPIClient
  @ObservedObject var cacheManager: AudioCacheManager
  let onTap: () -> Void

  // 收藏状态相关
  @ObservedObject private var favoriteManager = FavoriteStatusManager.shared
  @State private var isTogglingStarred: Bool = false

  // 计算属性：从全局管理器获取收藏状态
  private var isStarred: Bool {
    favoriteManager.isFavorite(songId: song.id)
  }
    
  var body: some View {
    HStack(spacing: 12) {
      // 专辑封面
      CoverArtView(
        coverArtId: song.coverArt ?? song.albumId,
        apiClient: apiClient,
        size: 50,
        cornerRadius: 8,
        showProgress: true
      )
      
      // 歌曲信息
      VStack(alignment: .leading, spacing: 4) {
        HStack {
          Text(song.displayTitle)
            .font(.headline)
            .foregroundColor(.primary)
            .lineLimit(1)

          Spacer()

          // 收藏状态指示器
          if isTogglingStarred {
            ProgressView()
              .scaleEffect(0.6)
              .frame(width: 12, height: 12)
          } else if isStarred {
            Image(systemName: "heart.fill")
              .foregroundColor(.red)
              .font(.caption)
          }

          // 缓存状态指示器
          let cacheKey = generateCacheKey(for: song)
          cacheStatusView(for: cacheKey)
            .onAppear {
              debugCacheStatus(for: cacheKey, songTitle: song.displayTitle)
            }

          Text(song.durationString)
            .font(.caption)
            .foregroundColor(.secondary)
        }
        
        HStack {
          Text(song.displayArtist)
            .font(.caption)
            .foregroundColor(.secondary)
            .lineLimit(1)
          
          if !song.displayAlbum.isEmpty {
            Text("•")
              .font(.caption)
              .foregroundColor(.secondary)
            
            Text(song.displayAlbum)
              .font(.caption)
              .foregroundColor(.secondary)
              .lineLimit(1)
          }
          
          Spacer()
          
          Text(song.fileSizeString)
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
      
      // 播放图标
      Image(systemName: isCurrentTrack ? (isPlaying ? "pause.circle.fill" : "play.circle.fill") : "play.circle")
        .font(.title2)
        .foregroundColor(isCurrentTrack ? .accentColor : .primary)
        .symbolEffect(.pulse, isActive: isCurrentTrack && isPlaying)
    }
    .padding(.vertical, 4)
    .contentShape(Rectangle()) // 使整行可点击
    .onTapGesture {
      onTap()
    }
    .onAppear {
      // 初始化收藏状态到全局管理器
      updateStarredStatus()
    }
  }
  
  @ViewBuilder
  private func cacheStatusView(for trackId: String) -> some View {
    let status = cacheManager.getCacheStatus(for: trackId)
    
    switch status {
    case .cached:
      Image(systemName: "checkmark.circle.fill")
        .foregroundColor(.green)
        .font(.caption)
    case .downloading:
      ProgressView()
        .scaleEffect(0.6)
        .frame(width: 12, height: 12)
    case .failed:
      Image(systemName: "exclamationmark.circle.fill")
        .foregroundColor(.red)
        .font(.caption)
    case .notCached:
      Image(systemName: "icloud.and.arrow.down")
        .foregroundColor(.secondary)
        .font(.caption)
    }
  }
  
  /// 为NavidromeSong生成复合缓存键
  private func generateCacheKey(for song: NavidromeSong) -> String {
    // 尝试获取当前的Navidrome配置
    let configManager = NavidromeConfigManager()
    guard let config = configManager.getLastUsedConfig() else {
      print("⚠️ 无法获取Navidrome配置，使用简单缓存键")
      return song.id
    }
    
    return cacheManager.generateCacheKey(songId: song.id, serverURL: config.serverURL, username: config.username)
  }
  
  /// 调试：打印缓存状态信息
  private func debugCacheStatus(for trackId: String, songTitle: String) {
    #if DEBUG
    let status = cacheManager.getCacheStatus(for: trackId)
    let cacheURL = cacheManager.getCachedURL(for: trackId)
    let debugInfo = """
    🔍 音乐库缓存状态调试
    歌曲: \(songTitle)
    TrackID: \(trackId)
    状态: \(status)
    缓存URL: \(cacheURL?.path ?? "无")
    缓存管理器状态数: \(cacheManager.cacheStatus.count)
    下载任务数: \(cacheManager.downloadTasks.count)
    """
    print(debugInfo)

    // 检查文件系统中是否真的存在这个文件
    if let url = cacheURL {
      let exists = FileManager.default.fileExists(atPath: url.path)
      print("📂 实际文件存在: \(exists)")
    }
    #endif
  }

  /// 更新收藏状态到全局管理器
  private func updateStarredStatus() {
    // 检查歌曲的starred字段是否不为空，并更新到全局管理器
    let isStarred = song.starred != nil && !song.starred!.isEmpty
    favoriteManager.updateFavoriteStatus(songId: song.id, isFavorite: isStarred)
  }
}
  
