//
//  PlaylistView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import SwiftUI

struct PlaylistView: View {
  @Environment(\.dismiss) private var dismiss
  @StateObject private var audioManager = AudioPlayerManager.shared
  @StateObject private var cacheManager = AudioCacheManager.shared
    
  var body: some View {
    VStack {
      // 顶部标题栏
      HStack {
        Button("完成") {
          dismiss()
        }
        .font(.body)
                
        Spacer()
                
        Text("播放列表")
          .font(.headline)
          .fontWeight(.semibold)
                
        Spacer()
                
        if !$audioManager.playlist.isEmpty {
          EditButton()
            .font(.body)
        } else {
          Button("") {}
            .opacity(0)
        }
      }
      .padding()
      .background(Color(UIColor.systemBackground))
            
      if audioManager.playlist.isEmpty {
        // 空状态
        VStack(spacing: 20) {
          Image(systemName: "music.note.list")
            .font(.system(size: 60))
            .foregroundColor(.secondary)
                    
          Text("播放列表为空")
            .font(.title2)
            .fontWeight(.medium)
            .foregroundColor(.primary)
                    
          Text("点击音乐库中的歌曲开始播放")
            .font(.body)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
      } else {
        // 播放列表信息
        VStack(alignment: .leading, spacing: 8) {
          HStack {
            Text("共 \(audioManager.playlist.count) 首歌曲")
              .font(.subheadline)
              .foregroundColor(.secondary)
                        
            Spacer()
                        
            Menu {
              Button("清空列表") {
                audioManager.clearPlaylist()
              }
                            
              Button("清空播放状态") {
                audioManager.clearPlaybackState()
              }
            } label: {
              Text("清空")
                .font(.caption)
                .foregroundColor(.red)
            }
          }
                    
          if let currentTrack = audioManager.currentTrack {
            Text("正在播放: \(currentTrack.name)")
              .font(.caption)
              .foregroundColor(.primary)
          }
        }
        .padding(.horizontal)
        .padding(.top, 8)
                
        // 播放列表
        List {
          ForEach(Array(audioManager.playlist.enumerated()), id: \.element.id) { index, track in
            PlaylistRowView(
              track: track,
              index: index,
              isCurrentTrack: index == audioManager.currentTrackIndex,
              isPlaying: audioManager.isPlaying && index == audioManager.currentTrackIndex,
              cacheManager: cacheManager
            ) {
              // 点击播放
              audioManager.currentTrackIndex = index
              audioManager.playCurrentTrack()
            }
          }
          .onDelete(perform: deleteItems)
          .onMove(perform: moveItems)
        }
        .listStyle(PlainListStyle())
      }
    }
    .background(Color(UIColor.systemGroupedBackground))
  }
    
  private func deleteItems(offsets: IndexSet) {
    for index in offsets {
      audioManager.removeFromPlaylist(at: index)
    }
  }
    
  private func moveItems(from source: IndexSet, to destination: Int) {
    // 简化处理：只支持单个项目移动
    if let sourceIndex = source.first {
      let destinationIndex = destination > sourceIndex ? destination - 1 : destination
      audioManager.moveTrack(from: sourceIndex, to: destinationIndex)
    }
  }
}

struct PlaylistRowView: View {
  let track: AudioFileInfo
  let index: Int
  let isCurrentTrack: Bool
  let isPlaying: Bool
  let cacheManager: AudioCacheManager
  let onTap: () -> Void
    
  var body: some View {
    Button(action: onTap) {
      HStack(spacing: 12) {
        // 序号或播放状态图标
        ZStack {
          Circle()
            .fill(isCurrentTrack ? Color.accentColor.opacity(0.2) : Color.clear)
            .frame(width: 30, height: 30)
                    
          if isCurrentTrack && isPlaying {
            Image(systemName: "speaker.wave.2.fill")
              .font(.caption)
              .foregroundColor(.accentColor)
              .symbolEffect(.pulse, isActive: true)
          } else if isCurrentTrack {
            Image(systemName: "pause.fill")
              .font(.caption)
              .foregroundColor(.accentColor)
          } else {
            Text("\(index + 1)")
              .font(.caption)
              .fontWeight(.medium)
              .foregroundColor(.secondary)
          }
        }
                
        // 歌曲信息
        VStack(alignment: .leading, spacing: 4) {
          HStack {
            Text(track.name)
              .font(.headline)
              .foregroundColor(isCurrentTrack ? .accentColor : .primary)
              .lineLimit(1)
            
            Spacer()
            
            // 缓存状态指示器
            if !track.url.isFileURL {
              // 生成复合缓存键
              let cacheKey = cacheManager.generateCacheKeyFromURL(track.url) ?? track.id.uuidString
              cacheStatusView(for: cacheKey)
            }
          }
                    
          HStack {
            Text(track.fileSizeString)
              .font(.caption)
              .foregroundColor(.secondary)
            
            if track.duration > 0 {
              Text("•")
                .font(.caption)
                .foregroundColor(.secondary)
              
              Text(track.durationString)
                .font(.caption)
                .foregroundColor(.secondary)
            }
                        
            Spacer()
                        
            Text(track.dateAddedString)
              .font(.caption)
              .foregroundColor(.secondary)
          }
        }
                
        Spacer()
                
        // 播放图标
        if isCurrentTrack {
          Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
            .font(.title3)
            .foregroundColor(.accentColor)
        } else {
          Image(systemName: "play.circle")
            .font(.title3)
            .foregroundColor(.secondary)
        }
      }
      .padding(.vertical, 4)
    }
    .buttonStyle(PlainButtonStyle())
  }
  
  /// 从Navidrome URL中提取歌曲ID
  private func extractSongIdFromURL(_ url: URL) -> String? {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    // 查找id参数
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }

  @ViewBuilder
  private func cacheStatusView(for trackId: String) -> some View {
    let status = cacheManager.getCacheStatus(for: trackId)
    
    switch status {
    case .cached:
      Image(systemName: "checkmark.circle.fill")
        .foregroundColor(.green)
        .font(.caption)
    case .downloading:
      ProgressView()
        .scaleEffect(0.6)
        .frame(width: 12, height: 12)
    case .failed:
      Image(systemName: "exclamationmark.circle.fill")
        .foregroundColor(.red)
        .font(.caption)
    case .notCached:
      Image(systemName: "icloud.and.arrow.down")
        .foregroundColor(.secondary)
        .font(.caption)
    }
  }
}

#Preview {
  PlaylistView()
}
