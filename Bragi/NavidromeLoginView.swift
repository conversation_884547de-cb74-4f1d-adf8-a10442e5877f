//
//  NavidromeLoginView.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import SwiftUI

// MARK: - Navidrome 登录视图

struct NavidromeLoginView: View {
  @Environment(\.dismiss) private var dismiss
  let apiClient: NavidromeAPIClient
  let configManager: NavidromeConfigManager
  let onLoginSuccess: () -> Void
  
  @State private var serverURL = ""
  @State private var username = ""
  @State private var password = ""
  @State private var isLogging = false
  @State private var showingAlert = false
  @State private var alertMessage = ""
  @State private var showingSavedConfigs = false
  
  var body: some View {
    NavigationView {
      VStack(spacing: 24) {
        VStack(spacing: 16) {
          Image(systemName: "music.note.house")
            .font(.system(size: 50))
            .foregroundColor(.purple)
          
          Text("登录 Navidrome")
            .font(.title2)
            .fontWeight(.semibold)
          
          Text("连接到您的 Navidrome 音乐服务器")
            .font(.body)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }
        .padding(.top, 40)
        
        VStack(spacing: 16) {
          // 保存的配置
          if !configManager.savedConfigs.isEmpty {
            VStack(alignment: .leading, spacing: 8) {
              HStack {
                Text("保存的服务器")
                  .font(.headline)
                  .foregroundColor(.primary)
                
                Spacer()
                
                Button(showingSavedConfigs ? "隐藏" : "显示") {
                  showingSavedConfigs.toggle()
                }
                .font(.caption)
                .foregroundColor(.blue)
              }
              
              if showingSavedConfigs {
                ScrollView(.horizontal, showsIndicators: false) {
                  HStack(spacing: 12) {
                    ForEach(Array(configManager.savedConfigs.enumerated()), id: \.offset) { index, config in
                      SavedConfigCard(
                        config: config,
                        onSelect: {
                          loadConfig(config)
                        },
                        onDelete: {
                          configManager.deleteConfig(at: index)
                        }
                      )
                    }
                  }
                  .padding(.horizontal, 4)
                }
              }
            }
          }
          
          VStack(alignment: .leading, spacing: 8) {
            Text("服务器地址")
              .font(.headline)
              .foregroundColor(.primary)
            
            TextField("https://navidrome.example.com", text: $serverURL)
              .textFieldStyle(RoundedBorderTextFieldStyle())
              .keyboardType(.URL)
              .autocapitalization(.none)
              .disableAutocorrection(true)
          }
          
          VStack(alignment: .leading, spacing: 8) {
            Text("用户名")
              .font(.headline)
              .foregroundColor(.primary)
            
            TextField("用户名", text: $username)
              .textFieldStyle(RoundedBorderTextFieldStyle())
              .autocapitalization(.none)
              .disableAutocorrection(true)
          }
          
          VStack(alignment: .leading, spacing: 8) {
            Text("密码")
              .font(.headline)
              .foregroundColor(.primary)
            
            SecureField("密码", text: $password)
              .textFieldStyle(RoundedBorderTextFieldStyle())
          }
        }
        .padding(.horizontal, 20)
        
        Button(action: performLogin) {
          HStack {
            if isLogging {
              ProgressView()
                .scaleEffect(0.8)
                .foregroundColor(.white)
            } else {
              Image(systemName: "arrow.right.circle.fill")
            }
            Text(isLogging ? "登录中..." : "登录")
          }
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 12)
          .background(
            LinearGradient(
              gradient: Gradient(colors: [Color.purple, Color.blue]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .cornerRadius(25)
        }
        .disabled(serverURL.isEmpty || username.isEmpty || password.isEmpty || isLogging)
        
        Spacer()
      }
      .navigationTitle("Navidrome 登录")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("取消") {
            dismiss()
          }
        }
      }
    }
    .alert("提示", isPresented: $showingAlert) {
      Button("确定", role: .cancel) {}
    } message: {
      Text(alertMessage)
    }
    .onAppear {
      loadLastUsedConfig()
    }
  }
  
  private func loadLastUsedConfig() {
    if let lastConfig = configManager.getLastUsedConfig() {
      serverURL = lastConfig.serverURL
      username = lastConfig.username
      password = lastConfig.password
    }
  }
  
  private func loadConfig(_ config: NavidromeConfig) {
    serverURL = config.serverURL
    username = config.username
    password = config.password
  }
  
  private func performLogin() {
    isLogging = true
    
    Task {
      do {
        try await apiClient.authenticate(
          serverURL: serverURL,
          username: username,
          password: password
        )
        
        await MainActor.run {
          // 保存成功的配置
          let config = NavidromeConfig(
            serverURL: serverURL,
            username: username,
            password: password,
            lastConnected: Date()
          )
          configManager.saveConfig(config)
          
          isLogging = false
          onLoginSuccess()
          dismiss()
        }
      } catch {
        await MainActor.run {
          isLogging = false
          alertMessage = error.localizedDescription
          showingAlert = true
        }
      }
    }
  }
}
  
