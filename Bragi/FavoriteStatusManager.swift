//
//  FavoriteStatusManager.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import Foundation
import Combine

// MARK: - 收藏状态管理器

class FavoriteStatusManager: ObservableObject {
  static let shared = FavoriteStatusManager()
  
  // 收藏状态缓存：songId -> isStarred
  @Published private var favoriteStatusCache: [String: Bool] = [:]
  
  private init() {}
  
  /// 获取歌曲的收藏状态
  func isFavorite(songId: String) -> Bool {
    return favoriteStatusCache[songId] ?? false
  }
  
  /// 更新歌曲的收藏状态
  func updateFavoriteStatus(songId: String, isFavorite: Bool) {
    favoriteStatusCache[songId] = isFavorite
    print("🔄 收藏状态已更新: \(songId) -> \(isFavorite)")
  }
  
  /// 切换歌曲的收藏状态
  func toggleFavoriteStatus(songId: String) -> Bool {
    let newStatus = !isFavorite(songId: songId)
    updateFavoriteStatus(songId: songId, isFavorite: newStatus)
    return newStatus
  }
  
  /// 批量更新收藏状态（用于从服务器获取收藏列表后更新）
  func updateFavoriteStatuses(from songs: [NavidromeSong]) {
    for song in songs {
      let isStarred = song.starred != nil && !song.starred!.isEmpty
      favoriteStatusCache[song.id] = isStarred
    }
    print("🔄 批量更新收藏状态: \(songs.count) 首歌曲")
  }
  
  /// 清空收藏状态缓存
  func clearCache() {
    favoriteStatusCache.removeAll()
    print("🗑️ 收藏状态缓存已清空")
  }
  
  /// 获取所有收藏的歌曲ID
  func getFavoriteSongIds() -> Set<String> {
    return Set(favoriteStatusCache.compactMap { key, value in
      value ? key : nil
    })
  }
}

// MARK: - 收藏状态通知

extension FavoriteStatusManager {
  /// 发布收藏状态变化通知
  func notifyFavoriteStatusChanged(songId: String, isFavorite: Bool) {
    NotificationCenter.default.post(
      name: .favoriteStatusChanged,
      object: nil,
      userInfo: [
        "songId": songId,
        "isFavorite": isFavorite
      ]
    )
  }
}

// MARK: - 通知名称

extension Notification.Name {
  static let favoriteStatusChanged = Notification.Name("favoriteStatusChanged")
}
