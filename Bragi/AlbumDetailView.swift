//
//  AlbumDetailView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import Foundation
import SwiftUI

// MARK: - 专辑详情页面

struct AlbumDetailView: View {
  let album: NavidromeAlbum
  let apiClient: NavidromeAPIClient
  let onDismissLibrary: (() -> Void)?
  
  @Environment(\.dismiss) private var dismiss
  @StateObject private var audioManager = AudioPlayerManager.shared
  @StateObject private var cacheManager = AudioCacheManager.shared
  @StateObject private var favoriteManager = FavoriteStatusManager.shared
  @State private var songs: [NavidromeSong] = []
  @State private var isLoading = false
  @State private var showingAlert = false
  @State private var alertMessage = ""
  @State private var searchText = ""
  
  var filteredSongs: [NavidromeSong] {
    let songsToFilter = songs.sorted { $0.id < $1.id } // 确保按ID排序
    
    if searchText.isEmpty {
      return songsToFilter
    } else {
      return songsToFilter.filter { song in
        song.displayTitle.localizedCaseInsensitiveContains(searchText) ||
          song.displayArtist.localizedCaseInsensitiveContains(searchText)
      }
    }
  }
  
  var body: some View {
    VStack(spacing: 0) {
      // 专辑头部信息
      albumHeaderView
      
      Divider()
      
      if isLoading {
        loadingView
      } else if songs.isEmpty {
        emptyStateView
      } else {
        songListView
      }
    }
    .navigationTitle(album.displayName)
    .navigationBarTitleDisplayMode(.large)
    .searchable(text: $searchText, prompt: "搜索歌曲")
    .toolbar {
      ToolbarItem(placement: .navigationBarTrailing) {
        HStack {
          Button(action: refreshSongs) {
            Image(systemName: "arrow.clockwise")
              .font(.title2)
          }
          .disabled(isLoading)
          
          if !songs.isEmpty {
            Button(action: playAllSongs) {
              Image(systemName: "play.fill")
                .font(.title2)
            }
          }
        }
      }
    }
    .alert("提示", isPresented: $showingAlert) {
      Button("确定", role: .cancel) {}
    } message: {
      Text(alertMessage)
    }
    .onAppear {
      Task {
        await loadSongs()
      }
    }
  }
  
  private var albumHeaderView: some View {
    HStack(spacing: 16) {
      // 专辑封面
      CoverArtView(
        coverArtId: album.coverArt,
        apiClient: apiClient,
        size: 100,
        cornerRadius: 12,
        showProgress: true
      )
      
      // 专辑信息
      VStack(alignment: .leading, spacing: 8) {
        Text(album.displayName)
          .font(.title2)
          .fontWeight(.bold)
          .foregroundColor(.primary)
          .lineLimit(2)
        
        Text(album.displayArtist)
          .font(.title3)
          .foregroundColor(.secondary)
          .lineLimit(1)
        
        HStack {
          if !album.yearString.isEmpty {
            Text(album.yearString)
              .font(.caption)
              .foregroundColor(.secondary)
            
            Text("•")
              .font(.caption)
              .foregroundColor(.secondary)
          }
          
          Text("\(album.songCount) 首歌曲")
            .font(.caption)
            .foregroundColor(.secondary)
          
          Text("•")
            .font(.caption)
            .foregroundColor(.secondary)
          
          Text(album.durationString)
            .font(.caption)
            .foregroundColor(.secondary)
        }
        
        if let genre = album.genre, !genre.isEmpty {
          Text("流派: \(genre)")
            .font(.caption)
            .foregroundColor(.secondary)
        }
        
        if let playCount = album.playCount, playCount > 0 {
          Text("播放次数: \(playCount)")
            .font(.caption2)
            .foregroundColor(.secondary)
        }
      }
      
      Spacer()
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
  }
  
  private var loadingView: some View {
    VStack(spacing: 20) {
      ProgressView()
        .scaleEffect(1.5)
      
      Text("正在加载歌曲...")
        .font(.headline)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var emptyStateView: some View {
    VStack(spacing: 20) {
      Image(systemName: "opticaldisc")
        .font(.system(size: 60))
        .foregroundColor(.secondary)
      
      Text("专辑为空")
        .font(.title2)
        .fontWeight(.medium)
        .foregroundColor(.primary)
      
      Text("这个专辑中还没有歌曲")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      Button(action: refreshSongs) {
        HStack {
          Image(systemName: "arrow.clockwise")
          Text("刷新")
        }
        .foregroundColor(.white)
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .background(
          LinearGradient(
            gradient: Gradient(colors: [Color.purple, Color.blue]),
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(25)
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var songListView: some View {
    List {
      ForEach(Array(filteredSongs.enumerated()), id: \.element.id) { index, song in
        HStack(spacing: 12) {
          // 曲目编号
          Text(song.track != nil ? "\(song.track!)" : "\(index + 1)")
            .font(.caption)
            .foregroundColor(.secondary)
            .frame(width: 25, alignment: .trailing)
          
          VStack(alignment: .leading, spacing: 4) {
            HStack {
              Text(song.displayTitle)
                .font(.headline)
                .foregroundColor(.primary)
                .lineLimit(1)

              Spacer()

              // 收藏状态指示器
              if favoriteManager.isFavorite(songId: song.id) {
                Image(systemName: "heart.fill")
                  .foregroundColor(.red)
                  .font(.caption)
              }

              // 缓存状态指示器
              let cacheKey = generateCacheKey(for: song)
              cacheStatusView(for: cacheKey)

              Text(song.durationString)
                .font(.caption)
                .foregroundColor(.secondary)
            }
            
            HStack {
              Text(song.displayArtist)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(1)
              
              Spacer()
              
              Text(song.fileSizeString)
                .font(.caption)
                .foregroundColor(.secondary)
            }
          }
          
          // 播放图标
          Image(systemName: isCurrentTrack(song) ?
            (audioManager.isPlaying ? "pause.circle.fill" : "play.circle.fill") : "play.circle")
            .font(.title2)
            .foregroundColor(isCurrentTrack(song) ? .accentColor : .primary)
            .symbolEffect(.pulse, isActive: isCurrentTrack(song) && audioManager.isPlaying)
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle()) // 使整行可点击
        .onTapGesture {
          Task {
            await playSong(song, at: index)
          }
        }
        .contextMenu {
          Button(action: {
            Task { await playSong(song, at: index) }
          }) {
            Label("播放", systemImage: "play.fill")
          }

          Button(action: {
            Task { await addSongToPlaylist(song) }
          }) {
            Label("添加到播放列表", systemImage: "plus")
          }

          // 收藏/取消收藏按钮
          if favoriteManager.isFavorite(songId: song.id) {
            Button(action: {
              Task { await unstarSong(song) }
            }) {
              Label("取消收藏", systemImage: "heart.slash")
            }
          } else {
            Button(action: {
              Task { await starSong(song) }
            }) {
              Label("收藏", systemImage: "heart")
            }
          }
        }
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadSongs()
    }
  }
  
  private func loadSongs() async {
    await MainActor.run {
      isLoading = true
    }
    
    do {
      let fetchedSongs = try await apiClient.fetchAlbumSongs(albumId: album.id)
      await MainActor.run {
        // 确保歌曲按ID排序
        self.songs = fetchedSongs.sorted { $0.id < $1.id }
        // 更新全局收藏状态管理器
        favoriteManager.updateFavoriteStatuses(from: fetchedSongs)
        self.isLoading = false
        print("📦 专辑歌曲已加载并按ID排序: \(self.songs.count) 首")
      }
    } catch {
      await MainActor.run {
        self.isLoading = false
        self.alertMessage = "加载专辑歌曲失败: \(error.localizedDescription)"
        self.showingAlert = true
      }
    }
  }
  
  private func refreshSongs() {
    Task {
      await loadSongs()
    }
  }
  
  private func playAllSongs() {
    guard !songs.isEmpty else { return }
    Task {
      await replacePlaylistAndPlay()
      // 播放开始后关闭整个音乐库页面
      await MainActor.run {
        onDismissLibrary?()
      }
    }
  }
  
  private func replacePlaylistAndPlay() async {
    // 将所有歌曲转换为AudioFileInfo
    var audioFiles: [AudioFileInfo] = []
    
    for song in songs {
      guard let streamURL = apiClient.getStreamURL(for: song) else {
        continue
      }
      
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      audioFiles.append(audioFileInfo)
    }
    
    await MainActor.run {
      if !audioFiles.isEmpty {
        audioManager.replacePlaylist(with: audioFiles, startFromIndex: 0)
        print("🎵 专辑播放列表已替换: \(album.displayName) - \(audioFiles.count)首歌曲")
      }
    }
  }
  
  private func playSong(_ song: NavidromeSong, at index: Int) async {
    guard let streamURL = apiClient.getStreamURL(for: song) else {
      await MainActor.run {
        alertMessage = "无法获取播放链接"
        showingAlert = true
      }
      return
    }
    
    await MainActor.run {
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      audioManager.playAudio(file: audioFileInfo)
    }
  }
  
  private func addSongToPlaylist(_ song: NavidromeSong) async {
    guard let streamURL = apiClient.getStreamURL(for: song) else {
      await MainActor.run {
        alertMessage = "无法获取播放链接"
        showingAlert = true
      }
      return
    }

    await MainActor.run {
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      audioManager.addToPlaylist(audioFileInfo)
    }
  }

  private func starSong(_ song: NavidromeSong) async {
    do {
      try await apiClient.starSong(songId: song.id)

      // 更新歌曲的收藏状态
      await MainActor.run {
        favoriteManager.updateFavoriteStatus(songId: song.id, isFavorite: true)
        updateSongStarredStatus(songId: song.id, isStarred: true)
        print("💖 已收藏歌曲: \(song.displayTitle)")
      }

    } catch {
      await MainActor.run {
        alertMessage = "收藏失败: \(error.localizedDescription)"
        showingAlert = true
      }
    }
  }

  private func unstarSong(_ song: NavidromeSong) async {
    do {
      try await apiClient.unstarSong(songId: song.id)

      // 更新歌曲的收藏状态
      await MainActor.run {
        favoriteManager.updateFavoriteStatus(songId: song.id, isFavorite: false)
        updateSongStarredStatus(songId: song.id, isStarred: false)
        print("💔 已取消收藏歌曲: \(song.displayTitle)")
      }

    } catch {
      await MainActor.run {
        alertMessage = "取消收藏失败: \(error.localizedDescription)"
        showingAlert = true
      }
    }
  }

  /// 更新歌曲的收藏状态
  private func updateSongStarredStatus(songId: String, isStarred: Bool) {
    let starredValue = isStarred ? Date().ISO8601Format() : nil

    // 更新歌曲列表中的收藏状态
    for i in 0..<songs.count {
      if songs[i].id == songId {
        songs[i] = NavidromeSong(
          id: songs[i].id,
          title: songs[i].title,
          artist: songs[i].artist,
          album: songs[i].album,
          track: songs[i].track,
          year: songs[i].year,
          genre: songs[i].genre,
          duration: songs[i].duration,
          size: songs[i].size,
          suffix: songs[i].suffix,
          albumId: songs[i].albumId,
          artistId: songs[i].artistId,
          path: songs[i].path,
          createdAt: songs[i].createdAt,
          updatedAt: songs[i].updatedAt,
          coverArt: songs[i].coverArt,
          starred: starredValue
        )
        break
      }
    }
  }
  
  // MARK: - 辅助方法
  
  /// 为NavidromeSong生成复合缓存键
  private func generateCacheKey(for song: NavidromeSong) -> String {
    // 尝试获取当前的Navidrome配置
    let configManager = NavidromeConfigManager()
    guard let config = configManager.getLastUsedConfig() else {
      print("⚠️ 无法获取Navidrome配置，使用简单缓存键")
      return song.id
    }
    
    return cacheManager.generateCacheKey(songId: song.id, serverURL: config.serverURL, username: config.username)
  }
  
  @ViewBuilder
  private func cacheStatusView(for trackId: String) -> some View {
    let status = cacheManager.getCacheStatus(for: trackId)
    
    switch status {
    case .cached:
      Image(systemName: "checkmark.circle.fill")
        .foregroundColor(.green)
        .font(.caption)
    case .downloading:
      ProgressView()
        .scaleEffect(0.6)
        .frame(width: 12, height: 12)
    case .failed:
      Image(systemName: "exclamationmark.circle.fill")
        .foregroundColor(.red)
        .font(.caption)
    case .notCached:
      Image(systemName: "icloud.and.arrow.down")
        .foregroundColor(.secondary)
        .font(.caption)
    }
  }
  
  private func isCurrentTrack(_ song: NavidromeSong) -> Bool {
    guard let currentTrack = audioManager.currentTrack else { return false }
    
    // 使用统一的显示名称格式进行比较
    let songDisplayName = createDisplayName(for: song)
    return currentTrack.name == songDisplayName
  }
  
  private func createDisplayName(for song: NavidromeSong) -> String {
    return "\(song.displayArtist) - \(song.displayTitle)"
  }
  
  private func createAudioFileInfo(from song: NavidromeSong, streamURL: URL) -> AudioFileInfo {
    // 直接使用song.id作为UUID，如果无法转换则生成随机UUID
    // 缓存系统现在直接使用song.id作为key，所以AudioFileInfo的UUID不重要
    let uuid: UUID
    if let existingUUID = UUID(uuidString: song.id) {
      uuid = existingUUID
    } else {
      uuid = UUID()
    }
    
    // 使用统一的显示名称格式
    let displayName = createDisplayName(for: song)
    
    // 处理空字符串问题
    let albumId = song.albumId.isEmpty ? nil : song.albumId
    let artistId = song.artistId.isEmpty ? nil : song.artistId
    
    return AudioFileInfo(
      id: uuid,
      name: displayName,
      fileName: "\(song.displayTitle).\(song.suffix)",
      url: streamURL,
      fileSize: song.size,
      dateAdded: Date(),
      duration: Double(song.duration), // 将Navidrome的时长（秒）转换为Double
      navidromeSongId: song.id,
      navidromeAlbumId: albumId,
      navidromeArtistId: artistId,
      albumName: song.displayAlbum,
      artistName: song.displayArtist
    )
  }
}

#Preview {
  NavigationView {
    AlbumDetailView(
      album: NavidromeAlbum(
        id: "1",
        name: "示例专辑",
        artist: "示例艺术家",
        artistId: "1",
        coverArt: nil,
        songCount: 12,
        duration: 3600,
        playCount: 50,
        created: "2024-01-01T00:00:00.000Z",
        year: 2024,
        genre: "流行"
      ),
      apiClient: NavidromeAPIClient(),
      onDismissLibrary: nil
    )
  }
}
