//
//  PlaylistDetailView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import Foundation
import SwiftUI

// MARK: - 播放列表详情页面

struct PlaylistDetailView: View {
  let playlist: NavidromePlaylist
  let apiClient: NavidromeAPIClient
  let onDismissLibrary: (() -> Void)?
  
  @Environment(\.dismiss) private var dismiss
  @StateObject private var audioManager = AudioPlayerManager.shared
  @StateObject private var cacheManager = AudioCacheManager.shared
  @StateObject private var favoriteManager = FavoriteStatusManager.shared
  @State private var songs: [NavidromeSong] = []
  @State private var isLoading = false
  @State private var showingAlert = false
  @State private var alertMessage = ""
  @State private var searchText = ""
  
  var filteredSongs: [NavidromeSong] {
    let songsToFilter = songs.sorted { $0.id < $1.id } // 确保按ID排序
    
    if searchText.isEmpty {
      return songsToFilter
    } else {
      return songsToFilter.filter { song in
        song.displayTitle.localizedCaseInsensitiveContains(searchText) ||
          song.displayArtist.localizedCaseInsensitiveContains(searchText) ||
          song.displayAlbum.localizedCaseInsensitiveContains(searchText)
      }
    }
  }
  
  var body: some View {
    VStack(spacing: 0) {
      // 播放列表头部信息
      playlistHeaderView
      
      Divider()
      
      if isLoading {
        loadingView
      } else if songs.isEmpty {
        emptyStateView
      } else {
        songListView
      }
    }
    .navigationTitle(playlist.displayName)
    .navigationBarTitleDisplayMode(.large)
    .searchable(text: $searchText, prompt: "搜索歌曲")
    .toolbar {
      ToolbarItem(placement: .navigationBarTrailing) {
        HStack {
          Button(action: refreshSongs) {
            Image(systemName: "arrow.clockwise")
              .font(.title2)
          }
          .disabled(isLoading)
          
          if !songs.isEmpty {
            Button(action: playAllSongs) {
              Image(systemName: "play.fill")
                .font(.title2)
            }
          }
        }
      }
    }
    .alert("提示", isPresented: $showingAlert) {
      Button("确定", role: .cancel) {}
    } message: {
      Text(alertMessage)
    }
    .onAppear {
      Task {
        await loadSongs()
      }
    }
  }
  
  private var playlistHeaderView: some View {
    HStack(spacing: 16) {
      // 播放列表封面
      CoverArtView(
        coverArtId: playlist.coverArt,
        apiClient: apiClient,
        size: 80,
        cornerRadius: 12,
        showProgress: true
      )
      
      // 播放列表信息
      VStack(alignment: .leading, spacing: 8) {
        Text(playlist.displayName)
          .font(.title2)
          .fontWeight(.bold)
          .foregroundColor(.primary)
          .lineLimit(2)
        
        if let owner = playlist.owner {
          Text("创建者: \(owner)")
            .font(.subheadline)
            .foregroundColor(.secondary)
        }
        
        HStack {
          Text("\(playlist.songCount) 首歌曲")
            .font(.caption)
            .foregroundColor(.secondary)
          
          if !playlist.durationString.isEmpty {
            Text("•")
              .font(.caption)
              .foregroundColor(.secondary)
            
            Text(playlist.durationString)
              .font(.caption)
              .foregroundColor(.secondary)
          }
        }
        
        if let comment = playlist.comment, !comment.isEmpty {
          Text(comment)
            .font(.caption)
            .foregroundColor(.secondary)
            .lineLimit(2)
        }
      }
      
      Spacer()
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
  }
  
  private var loadingView: some View {
    VStack(spacing: 20) {
      ProgressView()
        .scaleEffect(1.5)
      
      Text("正在加载歌曲...")
        .font(.headline)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var emptyStateView: some View {
    VStack(spacing: 20) {
      Image(systemName: "music.note.list")
        .font(.system(size: 60))
        .foregroundColor(.secondary)
      
      Text("播放列表为空")
        .font(.title2)
        .fontWeight(.medium)
        .foregroundColor(.primary)
      
      Text("这个播放列表中还没有歌曲")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      Button(action: refreshSongs) {
        HStack {
          Image(systemName: "arrow.clockwise")
          Text("刷新")
        }
        .foregroundColor(.white)
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .background(
          LinearGradient(
            gradient: Gradient(colors: [Color.purple, Color.blue]),
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(25)
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var songListView: some View {
    List {
      ForEach(Array(filteredSongs.enumerated()), id: \.element.id) { index, song in
        HStack(spacing: 12) {
          // 曲目编号
          Text("\(index + 1)")
            .font(.caption)
            .foregroundColor(.secondary)
            .frame(width: 20, alignment: .trailing)
          
          NavidromeSongRow(
            song: song,
            isCurrentTrack: isCurrentTrack(song),
            isPlaying: audioManager.isPlaying && isCurrentTrack(song),
            apiClient: apiClient,
            cacheManager: cacheManager
          ) {
            Task {
              await playSong(song, at: index)
            }
          }
        }
        .contextMenu {
          Button(action: {
            Task { await playSong(song, at: index) }
          }) {
            Label("播放", systemImage: "play.fill")
          }

          Button(action: {
            Task { await addSongToPlaylist(song) }
          }) {
            Label("添加到播放列表", systemImage: "plus")
          }

          // 收藏/取消收藏按钮
          if favoriteManager.isFavorite(songId: song.id) {
            Button(action: {
              Task { await unstarSong(song) }
            }) {
              Label("取消收藏", systemImage: "heart.slash")
            }
          } else {
            Button(action: {
              Task { await starSong(song) }
            }) {
              Label("收藏", systemImage: "heart")
            }
          }
        }
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadSongs()
    }
  }
  
  private func loadSongs() async {
    await MainActor.run {
      isLoading = true
    }
    
    do {
      let fetchedSongs = try await apiClient.fetchPlaylistSongs(playlistId: playlist.id)
      await MainActor.run {
        // 确保歌曲按ID排序
        self.songs = fetchedSongs.sorted { $0.id < $1.id }
        // 更新全局收藏状态管理器
        favoriteManager.updateFavoriteStatuses(from: fetchedSongs)
        self.isLoading = false
        print("📦 播放列表歌曲已加载并按ID排序: \(self.songs.count) 首")
      }
    } catch {
      await MainActor.run {
        self.isLoading = false
        self.alertMessage = "加载播放列表歌曲失败: \(error.localizedDescription)"
        self.showingAlert = true
      }
    }
  }
  
  private func refreshSongs() {
    Task {
      await loadSongs()
    }
  }
  
  private func playAllSongs() {
    guard !songs.isEmpty else { return }
    Task {
      await replacePlaylistAndPlay()
      // 播放开始后关闭整个音乐库页面
      await MainActor.run {
        onDismissLibrary?()
      }
    }
  }
  
  private func replacePlaylistAndPlay() async {
    // 将所有歌曲转换为AudioFileInfo
    var audioFiles: [AudioFileInfo] = []
    
    for song in songs {
      guard let streamURL = apiClient.getStreamURL(for: song) else {
        continue
      }
      
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      audioFiles.append(audioFileInfo)
    }
    
    await MainActor.run {
      if !audioFiles.isEmpty {
        audioManager.replacePlaylist(with: audioFiles, startFromIndex: 0)
        print("🎵 播放列表已替换: \(playlist.displayName) - \(audioFiles.count)首歌曲")
      }
    }
  }
  
  private func playSong(_ song: NavidromeSong, at index: Int) async {
    guard let streamURL = apiClient.getStreamURL(for: song) else {
      await MainActor.run {
        alertMessage = "无法获取播放链接"
        showingAlert = true
      }
      return
    }
    
    await MainActor.run {
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      audioManager.playAudio(file: audioFileInfo)
    }
  }
  
  private func addSongToPlaylist(_ song: NavidromeSong) async {
    guard let streamURL = apiClient.getStreamURL(for: song) else {
      await MainActor.run {
        alertMessage = "无法获取播放链接"
        showingAlert = true
      }
      return
    }

    await MainActor.run {
      let audioFileInfo = createAudioFileInfo(from: song, streamURL: streamURL)
      audioManager.addToPlaylist(audioFileInfo)
    }
  }

  private func starSong(_ song: NavidromeSong) async {
    do {
      try await apiClient.starSong(songId: song.id)

      // 更新歌曲的收藏状态
      await MainActor.run {
        favoriteManager.updateFavoriteStatus(songId: song.id, isFavorite: true)
        updateSongStarredStatus(songId: song.id, isStarred: true)
        print("💖 已收藏歌曲: \(song.displayTitle)")
      }

    } catch {
      await MainActor.run {
        alertMessage = "收藏失败: \(error.localizedDescription)"
        showingAlert = true
      }
    }
  }

  private func unstarSong(_ song: NavidromeSong) async {
    do {
      try await apiClient.unstarSong(songId: song.id)

      // 更新歌曲的收藏状态
      await MainActor.run {
        favoriteManager.updateFavoriteStatus(songId: song.id, isFavorite: false)
        updateSongStarredStatus(songId: song.id, isStarred: false)
        print("💔 已取消收藏歌曲: \(song.displayTitle)")
      }

    } catch {
      await MainActor.run {
        alertMessage = "取消收藏失败: \(error.localizedDescription)"
        showingAlert = true
      }
    }
  }

  /// 更新歌曲的收藏状态
  private func updateSongStarredStatus(songId: String, isStarred: Bool) {
    let starredValue = isStarred ? Date().ISO8601Format() : nil

    // 更新歌曲列表中的收藏状态
    for i in 0..<songs.count {
      if songs[i].id == songId {
        songs[i] = NavidromeSong(
          id: songs[i].id,
          title: songs[i].title,
          artist: songs[i].artist,
          album: songs[i].album,
          track: songs[i].track,
          year: songs[i].year,
          genre: songs[i].genre,
          duration: songs[i].duration,
          size: songs[i].size,
          suffix: songs[i].suffix,
          albumId: songs[i].albumId,
          artistId: songs[i].artistId,
          path: songs[i].path,
          createdAt: songs[i].createdAt,
          updatedAt: songs[i].updatedAt,
          coverArt: songs[i].coverArt,
          starred: starredValue
        )
        break
      }
    }
  }
  
  // MARK: - 辅助方法
  
  private func isCurrentTrack(_ song: NavidromeSong) -> Bool {
    guard let currentTrack = audioManager.currentTrack else { return false }
    
    // 使用统一的显示名称格式进行比较
    let songDisplayName = createDisplayName(for: song)
    return currentTrack.name == songDisplayName
  }
  
  private func createDisplayName(for song: NavidromeSong) -> String {
    return "\(song.displayArtist) - \(song.displayTitle)"
  }
  
  private func createAudioFileInfo(from song: NavidromeSong, streamURL: URL) -> AudioFileInfo {
    // 直接使用song.id作为UUID，如果无法转换则生成随机UUID
    // 缓存系统现在直接使用song.id作为key，所以AudioFileInfo的UUID不重要
    let uuid: UUID
    if let existingUUID = UUID(uuidString: song.id) {
      uuid = existingUUID
    } else {
      uuid = UUID()
    }
    
    // 使用统一的显示名称格式
    let displayName = createDisplayName(for: song)
    
    // 处理空字符串问题
    let albumId = song.albumId.isEmpty ? nil : song.albumId
    let artistId = song.artistId.isEmpty ? nil : song.artistId
    
    return AudioFileInfo(
      id: uuid,
      name: displayName,
      fileName: "\(song.displayTitle).\(song.suffix)",
      url: streamURL,
      fileSize: song.size,
      dateAdded: Date(),
      duration: Double(song.duration), // 将Navidrome的时长（秒）转换为Double
      navidromeSongId: song.id,
      navidromeAlbumId: albumId,
      navidromeArtistId: artistId,
      albumName: song.displayAlbum,
      artistName: song.displayArtist
    )
  }
}

#Preview {
  NavigationView {
    PlaylistDetailView(
      playlist: NavidromePlaylist(
        id: "1",
        name: "示例播放列表",
        comment: "这是一个示例播放列表",
        owner: "用户",
        isPublic: false,
        songCount: 10,
        duration: 2400,
        created: "2024-01-01T00:00:00.000Z",
        changed: nil,
        coverArt: nil
      ),
      apiClient: NavidromeAPIClient(),
      onDismissLibrary: nil
    )
  }
}
