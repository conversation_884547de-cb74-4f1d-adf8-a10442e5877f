//
//  NetworkSettingsView.swift
//  Bragi
//
//  Created by <PERSON> on 6/21/25.
//

import SwiftUI

// MARK: - 网络设置视图

struct NetworkSettingsView: View {
  @Environment(\.dismiss) private var dismiss
  @ObservedObject private var audioManager = AudioPlayerManager.shared
  @StateObject private var libraryCacheManager = MusicLibraryCacheManager.shared
  
  @State private var showingClearCacheAlert = false
  @State private var cacheInfo = ""

  var body: some View {
    NavigationView {
      Form {
        Section(header: Text("播放设置")) {
          HStack {
            Text("播放状态")
            Spacer()
            if audioManager.isPlaying {
              Text("正在播放")
                .foregroundColor(.green)
            } else {
              Text("暂停")
                .foregroundColor(.secondary)
            }
          }
          
          HStack {
            Text("网络状态")
            Spacer()
            switch audioManager.networkStatus {
            case .good:
              Text("良好")
                .foregroundColor(.green)
            case .poor:
              Text("较差")
                .foregroundColor(.orange)
            case .offline:
              Text("离线")
                .foregroundColor(.red)
            case .unknown:
              Text("未知")
                .foregroundColor(.secondary)
            }
          }
        }
        
        Section(header: Text("音乐库缓存")) {
          VStack(alignment: .leading, spacing: 8) {
            HStack {
              Text("缓存状态")
                .font(.headline)
              Spacer()
              Button("刷新") {
                updateCacheInfo()
              }
              .font(.caption)
            }
            
            if !cacheInfo.isEmpty {
              Text(cacheInfo)
                .font(.caption)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
            }
          }
          
          Button(action: { showingClearCacheAlert = true }) {
            HStack {
              Image(systemName: "trash")
              Text("清空缓存")
            }
            .foregroundColor(.red)
          }
        }
        
        Section(header: Text("缓存说明")) {
          VStack(alignment: .leading, spacing: 8) {
            Text("智能缓存机制")
              .font(.headline)
            
            Text("• 首次访问时从缓存快速加载，同时刷新数据")
            Text("• 只有当数据发生变化时才会更新界面显示")
            Text("• 缓存按服务器和用户分别存储")
            Text("• 缓存会在30分钟后自动过期")
            Text("• 网络异常时优先显示缓存数据")
          }
          .font(.caption)
          .foregroundColor(.secondary)
        }
        
        Section(header: Text("播放缓存")) {
          VStack(alignment: .leading, spacing: 8) {
            Text("音频缓存说明")
              .font(.headline)
            
            Text("• 播放的歌曲会自动缓存到本地")
            Text("• 缓存的歌曲支持离线播放")
            Text("• 智能预缓存下一首歌曲")
            Text("• 可在缓存管理中查看和清理")
          }
          .font(.caption)
          .foregroundColor(.secondary)
        }
      }
      .navigationTitle("设置与缓存")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("完成") {
            dismiss()
          }
        }
      }
      .onAppear {
        updateCacheInfo()
      }
      .alert("清空缓存", isPresented: $showingClearCacheAlert) {
        Button("取消", role: .cancel) {}
        Button("清空", role: .destructive) {
          libraryCacheManager.clearAllCache()
          updateCacheInfo()
          // 清空缓存后重置状态，下次会重新从网络加载
        }
      } message: {
        Text("这将清空所有音乐库缓存数据。下次访问时需要重新从网络加载。")
      }
    }
  }
  
  private func updateCacheInfo() {
    cacheInfo = libraryCacheManager.getCacheInfo()
  }
}
