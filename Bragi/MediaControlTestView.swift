//
//  MediaControlTestView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import MediaPlayer
import SwiftUI

struct MediaControlTestView: View {
  @StateObject private var audioManager = AudioPlayerManager.shared
    
  var body: some View {
    VStack(spacing: 30) {
      Text("系统媒体控制测试")
        .font(.title)
        .fontWeight(.bold)
            
      if let currentTrack = audioManager.currentTrack {
        VStack(spacing: 15) {
          Text("当前播放")
            .font(.headline)
            .foregroundColor(.secondary)
                    
          Text(currentTrack.name)
            .font(.title2)
            .fontWeight(.medium)
                    
          Text("播放状态: \(audioManager.isPlaying ? "播放中" : "已暂停")")
            .font(.body)
            .foregroundColor(audioManager.isPlaying ? .green : .orange)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
      } else {
        Text("没有正在播放的音乐")
          .font(.body)
          .foregroundColor(.secondary)
      }
            
      VStack(spacing: 20) {
        Text("测试说明")
          .font(.headline)
                
        VStack(alignment: .leading, spacing: 8) {
          HStack {
            Image(systemName: "control.center")
            Text("从屏幕右上角下拉打开控制中心")
          }
                    
          HStack {
            Image(systemName: "lock")
            Text("锁屏状态下查看媒体控制")
          }
                    
          HStack {
            Image(systemName: "airpodspro")
            Text("使用AirPods或有线耳机的按钮控制")
          }
                    
          HStack {
            Image(systemName: "car")
            Text("连接CarPlay时的车载控制")
          }
        }
        .font(.caption)
        .foregroundColor(.secondary)
      }
      .padding()
      .background(Color.blue.opacity(0.1))
      .cornerRadius(12)
            
      Text("现在播放音乐后，尝试使用系统控件！")
        .font(.footnote)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
            
      Spacer()
    }
    .padding()
    .navigationTitle("媒体控制测试")
    .navigationBarTitleDisplayMode(.inline)
  }
}

#Preview {
  NavigationView {
    MediaControlTestView()
  }
}
