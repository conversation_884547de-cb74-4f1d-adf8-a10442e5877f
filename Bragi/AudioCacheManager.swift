//
//  AudioCacheManager.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import AVFoundation
import Combine
import Foundation

// MARK: - 缓存状态枚举

enum AudioCacheStatus {
  case notCached
  case downloading
  case cached
  case failed
}

// MARK: - 歌曲信息结构

struct CachedSongInfo: Codable {
  let songId: String?
  let title: String
  let artist: String
  let album: String?
  let serverURL: String?
  let username: String?
  
  // 判断是否为Navidrome歌曲
  var isNavidromeSong: Bool {
    return songId != nil && serverURL != nil && username != nil
  }
}

// MARK: - 缓存项数据结构

struct CacheItem: Codable {
  let trackId: String
  let originalURL: URL
  let cachedURL: URL // 注意：实际使用时会根据trackId重新构建路径，不直接依赖此字段
  let fileSize: Int64
  let downloadDate: Date
  let lastAccessDate: Date
  
  var isExpired: Bool {
    // 缓存7天后过期
    let sevenDaysInSeconds: TimeInterval = 7 * 24 * 60 * 60
    let timeSinceLastAccess = Date().timeIntervalSince(lastAccessDate)
    return timeSinceLastAccess > sevenDaysInSeconds
  }
}

// MARK: - 下载任务

class AudioDownloadTask: NSObject, ObservableObject, URLSessionDownloadDelegate {
  let trackId: String
  let url: URL
  @Published var progress: Double = 0.0
  @Published var status: AudioCacheStatus = .downloading
  @Published var downloadedSize: Int64 = 0
  @Published var totalSize: Int64 = 0
  
  private var downloadTask: URLSessionDownloadTask?
  private var completion: ((Result<URL, Error>) -> Void)?
  private var urlSession: URLSession?
  
  init(trackId: String, url: URL) {
    self.trackId = trackId
    self.url = url
    super.init()
  }
  
  func start(completion: @escaping (Result<URL, Error>) -> Void) {
    self.completion = completion
    
    urlSession = URLSession(configuration: .default, delegate: self, delegateQueue: nil)
    downloadTask = urlSession?.downloadTask(with: url)
    
    downloadTask?.resume()
    status = .downloading
  }
  
  func cancel() {
    downloadTask?.cancel()
    urlSession?.invalidateAndCancel()
    status = .failed
  }
  
  // MARK: - URLSessionDownloadDelegate
  
  func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didFinishDownloadingTo location: URL) {
    // 立即处理文件移动，不要等待主线程切换
    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
    let cacheDirectory = documentsPath.appendingPathComponent("AudioCache")
    let finalURL = cacheDirectory.appendingPathComponent("\(trackId).m4a")
    
    do {
      // 确保缓存目录存在
      try FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true, attributes: nil)
      
      // 如果目标文件已存在，先删除
      if FileManager.default.fileExists(atPath: finalURL.path) {
        try FileManager.default.removeItem(at: finalURL)
      }
      
      // 立即移动文件
      try FileManager.default.moveItem(at: location, to: finalURL)
      
      DispatchQueue.main.async {
        self.status = .cached
        self.completion?(.success(finalURL))
      }
    } catch {
      DispatchQueue.main.async {
        self.status = .failed
        self.completion?(.failure(error))
      }
    }
  }
  
  func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didWriteData bytesWritten: Int64, totalBytesWritten: Int64, totalBytesExpectedToWrite: Int64) {
    DispatchQueue.main.async {
      self.downloadedSize = totalBytesWritten
      self.totalSize = totalBytesExpectedToWrite
      
      if totalBytesExpectedToWrite > 0 {
        self.progress = Double(totalBytesWritten) / Double(totalBytesExpectedToWrite)
      }
    }
  }
  
  func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
    DispatchQueue.main.async {
      if let error = error {
        self.status = .failed
        self.completion?(.failure(error))
      }
    }
  }
}

// MARK: - 音频缓存管理器

class AudioCacheManager: ObservableObject {
  static let shared = AudioCacheManager()
  
  // MARK: - Published Properties

  @Published var cacheSize: Int64 = 0
  @Published var downloadTasks: [String: AudioDownloadTask] = [:]
  @Published var cacheStatus: [String: AudioCacheStatus] = [:]
  
  // MARK: - Private Properties

  private let cacheDirectory: URL
  private let metadataFile: URL
  private var cacheItems: [String: CacheItem] = [:]
  private let maxConcurrentDownloads = 2
  
  // MARK: - Configurable Properties

  @Published var maxCacheSize: Int64 = 1024 * 1024 * 1024 // 默认1GB，可配置
  private var downloadQueue: [String] = []
  private var activeDownloads: Set<String> = []
  
  // MARK: - 预缓存设置

  private let enablePrefetch = true
  private var lastPlayingTrackIndex: Int = -1 // 跟踪上次播放的歌曲索引
  
  init() {
    // 创建缓存目录
    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
    cacheDirectory = documentsPath.appendingPathComponent("AudioCache")
    metadataFile = cacheDirectory.appendingPathComponent("cache_metadata.json")
    
    // 加载用户设置的缓存大小
    loadCacheSizeSettings()
    
    createCacheDirectory()
    loadCacheMetadata()
    calculateCacheSize()
    
    // 启动时清理过期缓存
    Task {
      await cleanExpiredCache()
      // 清理孤立的缓存文件（使用旧的hashValue生成的文件）
      cleanOrphanedCacheFiles()
    }
  }
  
  // MARK: - Private Helper Methods
  
  /// 从NavidromeSong创建CachedSongInfo
  private func createSongInfo(from song: NavidromeSong) -> CachedSongInfo {
    let configManager = NavidromeConfigManager()
    let config = configManager.getLastUsedConfig()
    
    return CachedSongInfo(
      songId: song.id,
      title: song.title,
      artist: song.artist,
      album: song.album,
      serverURL: config?.serverURL,
      username: config?.username
    )
  }
  
  /// 从URL和基本信息创建CachedSongInfo
  private func createSongInfo(from url: URL, title: String = "Unknown", artist: String = "Unknown") -> CachedSongInfo {
    // 尝试从URL提取Navidrome信息
    if let songId = extractSongIdFromURL(url) {
      let configManager = NavidromeConfigManager()
      let config = configManager.getLastUsedConfig()
      
      return CachedSongInfo(
        songId: songId,
        title: title,
        artist: artist,
        album: nil,
        serverURL: config?.serverURL,
        username: config?.username
      )
    } else {
      // 非Navidrome歌曲
      return CachedSongInfo(
        songId: nil,
        title: title,
        artist: artist,
        album: nil,
        serverURL: nil,
        username: nil
      )
    }
  }
  
  // MARK: - Public Methods
  
  /// 获取歌曲的缓存状态
  func getCacheStatus(for trackId: String) -> AudioCacheStatus {
    let status = cacheStatus[trackId] ?? .notCached
    #if DEBUG
    // 每10次查询打印一次状态摘要，避免日志过多
    if arc4random_uniform(10) == 0 {
      print("🔍 缓存状态查询: \(trackId.prefix(8))... → \(status)")
      print("📊 当前缓存状态总览: \(cacheStatus.count)个条目")
    }
    #endif
    return status
  }
  
  /// 获取缓存的音频文件URL
  func getCachedURL(for trackId: String) -> URL? {
    // 直接根据trackId构建缓存文件路径
    let cachedURL = cacheDirectory.appendingPathComponent("\(trackId).m4a")
    
    // 检查文件是否存在
    guard FileManager.default.fileExists(atPath: cachedURL.path) else {
      // 如果文件不存在，清理相关的缓存记录
      if cacheItems[trackId] != nil {
        print("🗑️ 缓存文件丢失，清理记录: \(trackId)")
        cacheItems.removeValue(forKey: trackId)
        cacheStatus[trackId] = .notCached
        saveCacheMetadata()
      }
      return nil
    }
    
    // 更新最后访问时间
    updateLastAccessDate(for: trackId)
    return cachedURL
  }
  
  /// 开始缓存音频（支持优先级）
  func cacheAudio(trackId: String, url: URL, priority: Bool = false) {
    let currentStatus = getCacheStatus(for: trackId)
    
    print("🗂️ 缓存请求: \(trackId.prefix(8))... 当前状态: \(currentStatus)")
    
    // 检查是否已经缓存或正在下载
    if currentStatus != .notCached {
      let statusText = currentStatus == .cached ? "已缓存" :
        currentStatus == .downloading ? "下载中" : "失败"
      print("⏭️ 跳过缓存音频(\(statusText)): \(trackId.prefix(8))...")
      return
    }
    
    // 检查是否是本地文件
    if url.isFileURL {
      print("⏭️ 跳过本地文件缓存: \(trackId.prefix(8))...")
      return
    }
    
    let priorityText = priority ? "高优先级" : "普通优先级"
    print("🗂️ 开始\(priorityText)缓存音频: \(trackId.prefix(8))... URL: \(url.absoluteString)")
    
    // 更新状态
    cacheStatus[trackId] = .downloading
    print("📊 状态已更新为下载中，当前状态字典大小: \(cacheStatus.count)")
    
    // 创建下载任务
    let downloadTask = AudioDownloadTask(trackId: trackId, url: url)
    downloadTasks[trackId] = downloadTask
    
    // 强制触发UI更新
    DispatchQueue.main.async {
      self.objectWillChange.send()
    }
    
    // 如果是高优先级或当前下载数量未达到上限，立即开始下载
    if priority || activeDownloads.count < maxConcurrentDownloads {
      startDownload(for: trackId, downloadTask: downloadTask)
    } else {
      // 添加到下载队列
      downloadQueue.append(trackId)
      print("📋 已加入下载队列: \(trackId.prefix(8))... (队列长度: \(downloadQueue.count))")
    }
  }
  
  /// 重置播放索引，用于新播放列表
  func resetPlayingTrackIndex() {
    lastPlayingTrackIndex = -1
    print("🔄 重置播放索引为 -1，确保新播放列表能正确触发缓存")
  }
  
  /// 智能预缓存：只在切换歌曲时缓存当前歌曲和下一首
  func smartPrefetch(_ playlist: [AudioFileInfo], currentIndex: Int) {
    guard enablePrefetch, currentIndex < playlist.count else {
      print("🚫 预缓存被跳过: enablePrefetch=\(enablePrefetch), currentIndex=\(currentIndex), playlist.count=\(playlist.count)")
      return
    }
    
    print("🎵 smartPrefetch调用: currentIndex=\(currentIndex), lastPlayingTrackIndex=\(lastPlayingTrackIndex)")
    
    // 检查是否切换到了新歌曲
    guard currentIndex != lastPlayingTrackIndex else {
      print("🔄 相同歌曲索引(\(currentIndex))，跳过缓存")
      return
    }
    
    print("🎵 切换到新歌曲索引: \(lastPlayingTrackIndex) → \(currentIndex)")
    lastPlayingTrackIndex = currentIndex
    
    // 缓存当前播放的歌曲（如果是网络歌曲且未缓存）
    let currentTrack = playlist[currentIndex]
    // 生成复合缓存键
    let currentTrackId = generateCacheKeyFromURL(currentTrack.url) ?? currentTrack.id.uuidString
    let currentStatus = getCacheStatus(for: currentTrackId)
    let isFileURL = currentTrack.url.isFileURL
    
    print("🎵 检查当前歌曲缓存: \(currentTrack.name)")
    print("   cacheKey: \(currentTrackId)")
    print("   isFileURL: \(isFileURL)")
    print("   status: \(currentStatus)")
    
    if !isFileURL && currentStatus == .notCached {
      print("🎵 开始缓存当前歌曲: \(currentTrack.name) cacheKey: \(currentTrackId)")
      cacheAudio(trackId: currentTrackId, url: currentTrack.url, priority: true)
    } else {
      let reason = isFileURL ? "本地文件" : "状态: \(currentStatus)"
      print("⏭️ 跳过当前歌曲缓存: \(reason)")
    }
    
    // 预缓存下一首歌曲（如果存在且未缓存）
    let nextIndex = currentIndex + 1
    if nextIndex < playlist.count {
      let nextTrack = playlist[nextIndex]
      let nextTrackId = generateCacheKeyFromURL(nextTrack.url) ?? nextTrack.id.uuidString
      
      // 只预缓存网络歌曲且未缓存的
      if !nextTrack.url.isFileURL && getCacheStatus(for: nextTrackId) == .notCached {
        print("🔮 开始预缓存下一首: \(nextTrack.name) cacheKey: \(nextTrackId)")
        cacheAudio(trackId: nextTrackId, url: nextTrack.url, priority: false)
        
        // 监听下一首的缓存完成状态
        observeNextTrackCacheCompletion(trackId: nextTrackId, trackName: nextTrack.name)
      } else if getCacheStatus(for: nextTrackId) == .cached {
        print("✅ 下一首已缓存，无需预缓存: \(nextTrack.name)")
      }
    } else {
      print("📋 已是播放列表最后一首，无下一首可缓存")
    }
  }
  
  /// 监听下一首歌曲的缓存完成状态
  private func observeNextTrackCacheCompletion(trackId: String, trackName: String) {
    // 使用定时器检查缓存状态
    Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] timer in
      guard let self = self else {
        timer.invalidate()
        return
      }
      
      let status = self.getCacheStatus(for: trackId)
      
      switch status {
      case .cached:
        print("✅ 下一首歌曲缓存完成: \(trackName)")
        print("⏸️ 缓存机制暂停，等待切换到下一首歌曲")
        timer.invalidate()
        
      case .failed:
        print("❌ 下一首歌曲缓存失败: \(trackName)")
        timer.invalidate()
        
      case .downloading:
        // 继续等待
        break
        
      case .notCached:
        // 如果状态变回未缓存，可能是被取消了
        print("⚠️ 下一首歌曲缓存被取消: \(trackName)")
        timer.invalidate()
      }
    }
  }
  
  /// 取消下载
  func cancelDownload(for trackId: String) {
    downloadTasks[trackId]?.cancel()
    downloadTasks.removeValue(forKey: trackId)
    cacheStatus[trackId] = .notCached
    activeDownloads.remove(trackId)
    
    // 从队列中移除
    downloadQueue.removeAll { $0 == trackId }
    
    // 启动队列中的下一个下载
    processDownloadQueue()
  }
  
  /// 清理所有缓存
  func clearAllCache() {
    // 取消所有下载
    for (trackId, _) in downloadTasks {
      cancelDownload(for: trackId)
    }
    
    // 删除所有缓存文件
    do {
      let files = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
      for file in files {
        try FileManager.default.removeItem(at: file)
      }
    } catch {
      print("清理缓存失败: \(error)")
    }
    
    // 重置状态
    cacheItems.removeAll()
    cacheStatus.removeAll()
    cacheSize = 0
    
    // 保存元数据
    saveCacheMetadata()
  }
  
  /// 清理过期缓存
  func cleanExpiredCache() async {
    let expiredItems = cacheItems.filter { $0.value.isExpired }
    
    for (trackId, item) in expiredItems {
      do {
        // 直接根据trackId构建文件路径
        let fileToDelete = cacheDirectory.appendingPathComponent("\(trackId).m4a")
        
        if FileManager.default.fileExists(atPath: fileToDelete.path) {
          try FileManager.default.removeItem(at: fileToDelete)
        }
        
        cacheItems.removeValue(forKey: trackId)
        cacheStatus[trackId] = .notCached
        print("清理过期缓存: \(trackId)")
      } catch {
        print("删除过期缓存失败: \(error)")
      }
    }
    
    // 重新计算缓存大小
    calculateCacheSize()
    saveCacheMetadata()
  }
  
  /// 智能缓存管理 - 根据存储空间自动清理
  func manageCacheSize() async {
    guard cacheSize > maxCacheSize else { return }
    
    // 按最后访问时间排序，删除最旧的
    let sortedItems = cacheItems.sorted { $0.value.lastAccessDate < $1.value.lastAccessDate }
    
    for (trackId, item) in sortedItems {
      do {
        // 直接根据trackId构建文件路径
        let fileToDelete = cacheDirectory.appendingPathComponent("\(trackId).m4a")
        
        if FileManager.default.fileExists(atPath: fileToDelete.path) {
          try FileManager.default.removeItem(at: fileToDelete)
        }
        
        cacheItems.removeValue(forKey: trackId)
        cacheStatus[trackId] = .notCached
        cacheSize -= item.fileSize
        
        print("清理缓存以释放空间: \(trackId)")
        
        if cacheSize <= Int64(Double(maxCacheSize) * 0.8) { // 清理到80%
          break
        }
      } catch {
        print("删除缓存文件失败: \(error)")
      }
    }
    
    saveCacheMetadata()
  }
  
  /// 打印当前缓存状态（用于调试）
  func printCacheStatus() {
    print("\n📊 ===== 缓存状态报告 =====")
    print("📦 缓存大小: \(formatBytes(cacheSize)) / \(formatBytes(maxCacheSize))")
    print("📥 正在下载: \(downloadTasks.count) 个文件")
    print("🎯 活跃下载: \(activeDownloads.count) 个")
    print("📋 下载队列: \(downloadQueue.count) 个等待中")
    print("💾 已缓存文件: \(cacheItems.count) 个")
    
    print("\n📑 缓存状态详情:")
    for (trackId, status) in cacheStatus.sorted(by: { $0.key < $1.key }) {
      let statusEmoji: String
      switch status {
      case .cached: statusEmoji = "✅"
      case .downloading: statusEmoji = "⏬"
      case .failed: statusEmoji = "❌"
      case .notCached: statusEmoji = "⭕"
      }
      
      // 验证文件实际存在性
      let expectedPath = cacheDirectory.appendingPathComponent("\(trackId).m4a")
      let fileExists = FileManager.default.fileExists(atPath: expectedPath.path)
      let existsEmoji = fileExists ? "📁" : "❗"
      
      print("  \(statusEmoji)\(existsEmoji) \(trackId.prefix(20))... : \(status)")
    }
    
    if !downloadTasks.isEmpty {
      print("\n📥 下载进度:")
      for (trackId, task) in downloadTasks {
        let progressPercent = Int(task.progress * 100)
        let progressBar = String(repeating: "█", count: progressPercent / 10) +
          String(repeating: "░", count: 10 - progressPercent / 10)
        print("  \(trackId.prefix(20))... : [\(progressBar)] \(progressPercent)%")
      }
    }
    
    print("\n🔍 说明: ✅=已缓存 ⏬=下载中 ❌=失败 ⭕=未缓存 📁=文件存在 ❗=文件缺失")
    print("========================\n")
  }
  
  /// 清理孤立的缓存文件（没有对应元数据的文件）
  func cleanOrphanedCacheFiles() {
    print("🧹 开始清理孤立的缓存文件...")
    
    do {
      let files = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
      var orphanedCount = 0
      var orphanedSize: Int64 = 0
      
      for file in files {
        guard file.pathExtension == "m4a" else { continue }
        
        let fileName = file.deletingPathExtension().lastPathComponent
        
        // 检查是否在缓存元数据中
        if !cacheItems.keys.contains(fileName) {
          // 这是一个孤立的文件
          let attributes = try file.resourceValues(forKeys: [.fileSizeKey])
          let fileSize = Int64(attributes.fileSize ?? 0)
          
          try FileManager.default.removeItem(at: file)
          orphanedCount += 1
          orphanedSize += fileSize
          
          print("🗑️ 删除孤立缓存文件: \(fileName) (\(formatBytes(fileSize)))")
        }
      }
      
      if orphanedCount > 0 {
        print("✅ 清理完成：删除了 \(orphanedCount) 个孤立文件，释放 \(formatBytes(orphanedSize)) 空间")
      } else {
        print("✅ 没有发现孤立的缓存文件")
      }
      
    } catch {
      print("❌ 清理孤立缓存文件失败: \(error)")
    }
  }
  
  // MARK: - Private Methods
  
  private func createCacheDirectory() {
    do {
      try FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
      print("📁 缓存目录已创建: \(cacheDirectory.path)")
    } catch {
      print("❌ 创建缓存目录失败: \(error)")
      print("❌ 目录路径: \(cacheDirectory.path)")
    }
  }
  
  private func startDownload(for trackId: String, downloadTask: AudioDownloadTask) {
    activeDownloads.insert(trackId)
    
    downloadTask.start { [weak self] result in
      DispatchQueue.main.async {
        self?.handleDownloadResult(trackId: trackId, result: result)
      }
    }
  }
  
  private func handleDownloadResult(trackId: String, result: Result<URL, Error>) {
    activeDownloads.remove(trackId)
    
    // 获取原始URL后再移除任务
    let originalURL = downloadTasks[trackId]?.url ?? URL(string: "unknown://")!
    downloadTasks.removeValue(forKey: trackId)
    
    switch result {
    case .success(let cachedURL):
      // 文件已经在delegate中移动完成，直接处理元数据
      do {
        // 获取文件大小
        let fileSize = try FileManager.default.attributesOfItem(atPath: cachedURL.path)[.size] as? Int64 ?? 0
        
        // 创建缓存项
        let cacheItem = CacheItem(
          trackId: trackId,
          originalURL: originalURL,
          cachedURL: cachedURL,
          fileSize: fileSize,
          downloadDate: Date(),
          lastAccessDate: Date()
        )
        
        cacheItems[trackId] = cacheItem
        cacheStatus[trackId] = .cached
        cacheSize += fileSize
        
        print("✅ 音频缓存完成: \(trackId), 大小: \(formatBytes(fileSize))")
        
        // 强制触发UI更新
        DispatchQueue.main.async {
          self.objectWillChange.send()
        }
        
        // 保存元数据
        saveCacheMetadata()
        
        // 检查缓存大小
        Task {
          await manageCacheSize()
        }
        
      } catch {
        print("❌ 处理缓存元数据失败: \(error)")
        cacheStatus[trackId] = .failed
      }
      
    case .failure(let error):
      print("❌ 下载失败: \(trackId) - \(error)")
      cacheStatus[trackId] = .failed
    }
    
    // 处理下载队列
    processDownloadQueue()
  }
  
  /// 从Navidrome URL中提取歌曲ID
  private func extractSongIdFromURL(_ url: URL) -> String? {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    // 查找id参数
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }
  
  // MARK: - Cache Key Generation
  
  /// 计算稳定的DJB2哈希值
  private func stableHash(for string: String) -> Int {
    return string.utf8.reduce(5381) { hash, char in
      ((hash << 5) &+ hash) &+ Int(char)
    }
  }
  
  /// 生成复合缓存键：song.id + navidrome服务器URL + 账户信息
  func generateCacheKey(songId: String, serverURL: String, username: String) -> String {
    // 方案1：使用稳定的DJB2哈希算法
    let compositeString = "\(songId)|\(serverURL)|\(username)"
    let djb2Hash = stableHash(for: compositeString)
    
    let cacheKey = "cache_\(abs(djb2Hash))"
    
    #if DEBUG
    // 缓存键生成完成
    #endif
    
    return cacheKey
  }
  
  /// 从Navidrome URL中提取歌曲ID并生成复合缓存键
  func generateCacheKeyFromURL(_ url: URL) -> String? {
    // 对于本地文件，使用文件路径作为缓存键
    if url.isFileURL {
      // 使用稳定的DJB2哈希
      let pathHash = stableHash(for: url.path)
      return "local_\(abs(pathHash))"
    }
    
    // 对于Navidrome流媒体
    if let songId = extractSongIdFromURL(url) {
      // 尝试获取当前的Navidrome配置
      let configManager = NavidromeConfigManager()
      if let config = configManager.getLastUsedConfig() {
        return generateCacheKey(songId: songId, serverURL: config.serverURL, username: config.username)
      } else {
        print("⚠️ 无法获取Navidrome配置，使用简单缓存键")
        return "navidrome_\(songId)"
      }
    }
    
    // 对于其他网络URL，使用URL的稳定哈希作为缓存键
    let urlString = url.absoluteString
    let urlHash = stableHash(for: urlString)
    return "web_\(abs(urlHash))"
  }
  
  /// 从缓存键反向查找歌曲ID
  func getSongIdFromCacheKey(_ cacheKey: String) -> String? {
    // 查找缓存元数据中匹配的项目
    for (key, item) in cacheItems {
      if key == cacheKey {
        // 尝试从原始URL提取歌曲ID
        if let songId = extractSongIdFromURL(item.originalURL) {
          return songId
        }
      }
    }
    
    // 如果在缓存元数据中找不到，尝试从缓存键本身推断
    // 对于简单的navidrome_songId格式
    if cacheKey.hasPrefix("navidrome_") {
      return String(cacheKey.dropFirst("navidrome_".count))
    }
    
    // 对于复合缓存键，我们需要检查所有可能的歌曲ID
    // 获取当前的Navidrome配置
    let configManager = NavidromeConfigManager()
    if let config = configManager.getLastUsedConfig() {
      // 遍历播放列表中的所有歌曲，找到匹配的缓存键
      // 这需要从外部传入播放列表，或者我们可以尝试从URL模式推断
      
      // 暂时返回nil，使用备选方案
      return nil
    }
    
    return nil
  }
  
  // MARK: - Cache Size Configuration
  
  /// 加载缓存大小设置
  private func loadCacheSizeSettings() {
    let savedSize = UserDefaults.standard.object(forKey: "AudioCacheMaxSize") as? Int64
    if let savedSize = savedSize, savedSize > 0 {
      maxCacheSize = savedSize
      print("📊 加载用户设置的缓存大小: \(formatBytes(maxCacheSize))")
    } else {
      // 默认1GB
      maxCacheSize = 1024 * 1024 * 1024
      print("📊 使用默认缓存大小: \(formatBytes(maxCacheSize))")
    }
  }
  
  /// 设置缓存大小限制
  func setCacheSizeLimit(_ sizeInBytes: Int64) {
    maxCacheSize = sizeInBytes
    UserDefaults.standard.set(sizeInBytes, forKey: "AudioCacheMaxSize")
    print("📊 缓存大小限制已设置为: \(formatBytes(maxCacheSize))")
    
    // 如果当前缓存超过新限制，立即清理
    Task {
      await manageCacheSize()
    }
  }
  
  /// 获取可用的缓存大小选项（以字节为单位）
  static func getCacheSizeOptions() -> [Int64] {
    return [
      1024 * 1024 * 1024, // 1GB
      5 * 1024 * 1024 * 1024, // 5GB
      10 * 1024 * 1024 * 1024, // 10GB
      20 * 1024 * 1024 * 1024 // 20GB
    ]
  }
  
  /// 获取缓存大小选项的显示文本
  static func getCacheSizeOptionTexts() -> [String] {
    return ["1GB", "5GB", "10GB", "20GB"]
  }

  private func processDownloadQueue() {
    guard activeDownloads.count < maxConcurrentDownloads,
          !downloadQueue.isEmpty else { return }
    
    let nextTrackId = downloadQueue.removeFirst()
    
    // 重新创建下载任务（之前的任务可能已经被清理）
    if let originalTask = downloadTasks[nextTrackId] {
      startDownload(for: nextTrackId, downloadTask: originalTask)
    }
  }
  
  private func updateLastAccessDate(for trackId: String) {
    guard let cacheItem = cacheItems[trackId] else { return }
    
    let updatedItem = CacheItem(
      trackId: cacheItem.trackId,
      originalURL: cacheItem.originalURL,
      cachedURL: cacheItem.cachedURL,
      fileSize: cacheItem.fileSize,
      downloadDate: cacheItem.downloadDate,
      lastAccessDate: Date()
    )
    
    cacheItems[trackId] = updatedItem
    
    // 异步保存元数据
    DispatchQueue.global(qos: .background).async {
      self.saveCacheMetadata()
    }
  }
  
  private func calculateCacheSize() {
    cacheSize = cacheItems.values.reduce(0) { $0 + $1.fileSize }
  }
  
  private func loadCacheMetadata() {
    guard FileManager.default.fileExists(atPath: metadataFile.path) else {
      print("📂 缓存元数据文件不存在，首次启动")
      return
    }
    
    do {
      let data = try Data(contentsOf: metadataFile)
      let decoder = JSONDecoder()
      decoder.dateDecodingStrategy = .iso8601
      let loadedItems = try decoder.decode([String: CacheItem].self, from: data)
      
      print("📂 加载缓存元数据文件，共\(loadedItems.count)个条目")
      
      // 验证缓存文件是否存在，清理无效项
      var validCount = 0
      for (trackId, item) in loadedItems {
        // 直接根据trackId构建期望的文件路径
        let expectedCachedURL = cacheDirectory.appendingPathComponent("\(trackId).m4a")
        
        if FileManager.default.fileExists(atPath: expectedCachedURL.path) {
          // 更新CacheItem使用正确的路径
          let updatedItem = CacheItem(
            trackId: item.trackId,
            originalURL: item.originalURL,
            cachedURL: expectedCachedURL,
            fileSize: item.fileSize,
            downloadDate: item.downloadDate,
            lastAccessDate: item.lastAccessDate
          )
          
          cacheItems[trackId] = updatedItem
          cacheStatus[trackId] = .cached
          validCount += 1
          print("✅ 验证缓存文件存在: \(trackId)")
        } else {
          print("❌ 缓存文件丢失，清理记录: \(trackId)")
        }
      }
      
      print("📊 缓存加载完成：有效缓存 \(validCount)/\(loadedItems.count)")
      
      // 重新计算缓存大小
      calculateCacheSize()
      print("📊 总缓存大小: \(formatBytes(cacheSize))")
      
      // 强制触发UI更新
      DispatchQueue.main.async {
        self.objectWillChange.send()
      }
      
    } catch {
      print("❌ 加载缓存元数据失败: \(error)")
      // 如果加载失败，尝试重新扫描缓存目录
      scanCacheDirectory()
    }
  }
  
  /// 扫描缓存目录并重建元数据
  private func scanCacheDirectory() {
    print("🔍 扫描缓存目录重建元数据...")
    
    do {
      let files = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey, .creationDateKey])
      
      for file in files {
        guard file.pathExtension == "m4a" else { continue }
        
        let trackId = file.deletingPathExtension().lastPathComponent
        let attributes = try file.resourceValues(forKeys: [.fileSizeKey, .creationDateKey])
        let fileSize = Int64(attributes.fileSize ?? 0)
        let creationDate = attributes.creationDate ?? Date()
        
        // 使用标准的文件路径构建方式
        let standardCachedURL = cacheDirectory.appendingPathComponent("\(trackId).m4a")
        
        let cacheItem = CacheItem(
          trackId: trackId,
          originalURL: URL(string: "unknown://")!,
          cachedURL: standardCachedURL,
          fileSize: fileSize,
          downloadDate: creationDate,
          lastAccessDate: creationDate
        )
        
        cacheItems[trackId] = cacheItem
        cacheStatus[trackId] = .cached
        print("🔍 发现缓存文件: \(trackId)")
      }
      
      print("🔍 扫描完成，发现 \(cacheItems.count) 个缓存文件")
      calculateCacheSize()
      saveCacheMetadata()
      
    } catch {
      print("❌ 扫描缓存目录失败: \(error)")
    }
  }
  
  private func formatBytes(_ bytes: Int64) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useGB, .useMB, .useKB]
    formatter.countStyle = .binary
    return formatter.string(fromByteCount: bytes)
  }
  
  private func saveCacheMetadata() {
    do {
      let encoder = JSONEncoder()
      encoder.dateEncodingStrategy = .iso8601
      let data = try encoder.encode(cacheItems)
      try data.write(to: metadataFile)
      print("💾 缓存元数据已保存: \(cacheItems.count)个条目, 总大小: \(formatBytes(cacheSize))")
      print("💾 保存路径: \(metadataFile.path)")
    } catch {
      print("❌ 保存缓存元数据失败: \(error)")
      print("❌ 保存路径: \(metadataFile.path)")
    }
  }
}
