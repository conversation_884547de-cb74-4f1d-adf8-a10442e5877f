//
//  AudioPlayerManager.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import AVFoundation
import Combine
import Foundation
import MediaPlayer
import SwiftUI

// TODO: Import types from shared module instead of duplicating
// For now, create a simple struct for backward compatibility
struct AudioFileInfo: Codable, Identifiable, Equatable {
  let id: UUID
  let name: String
  let fileName: String
  let url: URL
  let fileSize: Int
  let dateAdded: Date
  let duration: Double // 歌曲时长（秒）
  
  // Navidrome相关信息
  let navidromeSongId: String?
  let navidromeAlbumId: String?
  let navidromeArtistId: String?
  let albumName: String?
  let artistName: String?
  
  // 为了向后兼容，提供默认值
  init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    id = try container.decode(UUID.self, forKey: .id)
    name = try container.decode(String.self, forKey: .name)
    fileName = try container.decode(String.self, forKey: .fileName)
    url = try container.decode(URL.self, forKey: .url)
    fileSize = try container.decode(Int.self, forKey: .fileSize)
    dateAdded = try container.decode(Date.self, forKey: .dateAdded)
    
    // 新字段提供默认值以保持向后兼容
    duration = try container.decodeIfPresent(Double.self, forKey: .duration) ?? 0.0
    
    navidromeSongId = try container.decodeIfPresent(String.self, forKey: .navidromeSongId)
    navidromeAlbumId = try container.decodeIfPresent(String.self, forKey: .navidromeAlbumId)
    navidromeArtistId = try container.decodeIfPresent(String.self, forKey: .navidromeArtistId)
    albumName = try container.decodeIfPresent(String.self, forKey: .albumName)
    artistName = try container.decodeIfPresent(String.self, forKey: .artistName)
  }
  
  // 便利初始化器，保持向后兼容
  init(
    id: UUID,
    name: String,
    fileName: String,
    url: URL,
    fileSize: Int,
    dateAdded: Date,
    duration: Double = 0.0,
    navidromeSongId: String? = nil,
    navidromeAlbumId: String? = nil,
    navidromeArtistId: String? = nil,
    albumName: String? = nil,
    artistName: String? = nil
  ) {
    self.id = id
    self.name = name
    self.fileName = fileName
    self.url = url
    self.fileSize = fileSize
    self.dateAdded = dateAdded
    self.duration = duration
    self.navidromeSongId = navidromeSongId
    self.navidromeAlbumId = navidromeAlbumId
    self.navidromeArtistId = navidromeArtistId
    self.albumName = albumName
    self.artistName = artistName
  }
  
  var fileSizeString: String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB]
    formatter.countStyle = .file
    return formatter.string(fromByteCount: Int64(fileSize))
  }
  
  var dateAddedString: String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .short
    return formatter.string(from: dateAdded)
  }
  
  var durationString: String {
    guard duration > 0 else { return "0:00" }
    let minutes = Int(duration) / 60
    let seconds = Int(duration) % 60
    return String(format: "%d:%02d", minutes, seconds)
  }
}

// 网络状态枚举
enum NetworkStatus {
  case unknown
  case good
  case poor
  case offline
}

// 循环播放模式枚举
enum RepeatMode: String, CaseIterable, Codable {
  case off // 不循环
  case one // 单曲循环
  case all // 列表循环
  
  var displayName: String {
    switch self {
    case .off: return "不循环"
    case .one: return "单曲循环"
    case .all: return "列表循环"
    }
  }
  
  var systemImageName: String {
    switch self {
    case .off: return "repeat"
    case .one: return "repeat.1"
    case .all: return "repeat"
    }
  }
}

// 播放状态数据模型
struct PlaybackState: Codable {
  let playlist: [AudioFileInfo]
  let currentTrackIndex: Int
  let currentTime: Double
  let volume: Double
  let lastSaveTime: Date
  let isShuffleEnabled: Bool
  let repeatMode: RepeatMode
  let originalPlaylist: [AudioFileInfo]
  
  // 为了向后兼容，提供默认值
  init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    playlist = try container.decode([AudioFileInfo].self, forKey: .playlist)
    currentTrackIndex = try container.decode(Int.self, forKey: .currentTrackIndex)
    currentTime = try container.decode(Double.self, forKey: .currentTime)
    volume = try container.decode(Double.self, forKey: .volume)
    lastSaveTime = try container.decode(Date.self, forKey: .lastSaveTime)
    
    // 新字段提供默认值以保持向后兼容
    isShuffleEnabled = try container.decodeIfPresent(Bool.self, forKey: .isShuffleEnabled) ?? false
    repeatMode = try container.decodeIfPresent(RepeatMode.self, forKey: .repeatMode) ?? .off
    originalPlaylist = try container.decodeIfPresent([AudioFileInfo].self, forKey: .originalPlaylist) ?? []
    
    // shuffledIndices 字段已弃用，为了向后兼容，忽略该字段
  }
  
  init(playlist: [AudioFileInfo], currentTrackIndex: Int, currentTime: Double, volume: Double, lastSaveTime: Date, isShuffleEnabled: Bool, repeatMode: RepeatMode, originalPlaylist: [AudioFileInfo]) {
    self.playlist = playlist
    self.currentTrackIndex = currentTrackIndex
    self.currentTime = currentTime
    self.volume = volume
    self.lastSaveTime = lastSaveTime
    self.isShuffleEnabled = isShuffleEnabled
    self.repeatMode = repeatMode
    self.originalPlaylist = originalPlaylist
  }
}

// 下载和缓存相关代码已删除

class AudioPlayerManager: NSObject, ObservableObject {
  static let shared = AudioPlayerManager()
    
  @Published var isPlaying = false
  @Published var currentTime: Double = 0
  @Published var duration: Double = 0
  @Published var currentTrack: AudioFileInfo?
  @Published var volume: Double = 0.7
  @Published var albumArtwork: UIImage?
  @Published var playlist: [AudioFileInfo] = []
  @Published var currentTrackIndex: Int = 0
  @Published var artworkCacheUpdated = false
  @Published var networkStatus: NetworkStatus = .unknown
  @Published var downloadProgress: Double = 0.0
  @Published var isWaitingForDownload: Bool = false
  @Published var isShuffleEnabled = false
  @Published var repeatMode: RepeatMode = .off
    
  private var audioPlayer: AVAudioPlayer?
  private var avPlayer: AVPlayer?
  private var playerItem: AVPlayerItem?
  private var timer: Timer?
  private var artworkCache: [String: UIImage] = [:]
  private var songInfoCache: [String: CachedSongInfo] = [:] // 新增：歌曲信息缓存
  private var cancellables = Set<AnyCancellable>()
  private var pendingPlayFile: AudioFileInfo? // 等待下载完成的文件
  
  // 乱序播放相关
  private var originalPlaylist: [AudioFileInfo] = [] // 原始播放列表（乱序前）
  private var shuffledIndices: [Int] = [] // 乱序后的索引映射
  
  // Navidrome API 客户端
  private var navidromeClient: NavidromeAPIClient?
  
  // 音频缓存管理器
  private let cacheManager = AudioCacheManager.shared
    
  // 添加播放状态记录属性
  private var wasPlayingBeforeSeek: Bool = false
  
  override init() {
    super.init()
    setupAudioSession()
    setupSystemVolumeObserver()
    syncSystemVolume()
    setupRemoteCommandCenter()
    setupNetworkMonitoring()
    setupNavidromeClient()
    restorePlaybackState() // 恢复上次的播放状态
  }
  
  deinit {
    AVAudioSession.sharedInstance().removeObserver(self, forKeyPath: "outputVolume")
    disableRemoteCommandCenter()
    savePlaybackState() // 应用退出时保存状态
  }
  
  private func setupNetworkMonitoring() {
    // 简单的网络状态监控
    Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
      self?.checkNetworkStatus()
    }
  }
  
  private func checkNetworkStatus() {
    // 简化的网络状态检测
    DispatchQueue.main.async {
      if self.isPlaying {
        self.networkStatus = .good
      } else {
        self.networkStatus = .unknown
      }
    }
  }
  
  private func setupNavidromeClient() {
    let configManager = NavidromeConfigManager()
    guard let lastConfig = configManager.getLastUsedConfig() else {
      return
    }
    
    let client = NavidromeAPIClient()
    Task {
      do {
        try await client.authenticate(
          serverURL: lastConfig.serverURL,
          username: lastConfig.username,
          password: lastConfig.password
        )
        
        await MainActor.run {
          self.navidromeClient = client
          print("AudioPlayerManager Navidrome客户端初始化成功")
        }
      } catch {
        print("AudioPlayerManager Navidrome认证失败: \(error.localizedDescription)")
      }
    }
  }
    
  private func setupAudioSession() {
    do {
      // 设置音频会话类别，支持后台播放和远程控制
      try AVAudioSession.sharedInstance().setCategory(
        .playback,
        mode: .default,
        options: [.allowAirPlay, .allowBluetooth, .allowBluetoothA2DP, .duckOthers]
      )
      try AVAudioSession.sharedInstance().setActive(true)
      
      print("✅ 音频会话设置成功，支持后台播放、远程控制和音量调节")
      print("📊 当前系统音量: \(AVAudioSession.sharedInstance().outputVolume)")
    } catch {
      print("❌ 音频会话设置失败: \(error.localizedDescription)")
    }
  }
    
  func playAudio(file: AudioFileInfo) {
    print("🎵 单独播放歌曲: \(file.name)")
    
    // 调试输出接收到的AudioFileInfo信息
    print("🔍 接收到的AudioFileInfo调试:")
    print("  navidromeSongId: \(file.navidromeSongId ?? "nil")")
    print("  navidromeAlbumId: \(file.navidromeAlbumId ?? "nil")")
    print("  navidromeArtistId: \(file.navidromeArtistId ?? "nil")")
    print("  albumName: \(file.albumName ?? "nil")")
    print("  artistName: \(file.artistName ?? "nil")")
    
    // 管理播放列表：将选中的歌曲移动到第一位
    managePlaylist(for: file)
    
    // 重置缓存管理器的播放索引，确保单独播放歌曲时能正确触发缓存
    cacheManager.resetPlayingTrackIndex()
        
    // 播放第一首歌曲（刚刚移动到第一位的歌曲）
    playCurrentTrack()
        
    // 自动保存播放状态
    autoSavePlaybackState()
  }
    
  func playCurrentTrack() {
    guard currentTrackIndex < playlist.count else { return }
        
    let file = playlist[currentTrackIndex]
        
    // 停止当前播放
    stop()
    
    // 检查是否是网络URL
    if file.url.scheme == "http" || file.url.scheme == "https" {
      // 对于网络URL，等待下载完成后播放本地缓存文件
      waitForDownloadAndPlay(file: file)
    } else {
      // 使用AVAudioPlayer播放本地文件
      playLocalAudio(file: file)
    }
  }
  
  private func waitForDownloadAndPlay(file: AudioFileInfo) {
    // 解析歌曲信息用于显示
    let components = file.name.components(separatedBy: " - ")
    let _ = components.count >= 2 ? components[0] : "未知艺术家"
    let title = components.count >= 2 ? components.dropFirst().joined(separator: " - ") : file.name
    
    print("🎵 准备下载并播放: \(title)")
    
    // 重置播放状态
    duration = 0
    currentTime = 0
    downloadProgress = 0.0
    isWaitingForDownload = true
    pendingPlayFile = file
    
    // 生成复合缓存键
    let cacheKey = cacheManager.generateCacheKeyFromURL(file.url) ?? file.id.uuidString
    
    // 检查是否已经有缓存版本
    if let cachedURL = cacheManager.getCachedURL(for: cacheKey) {
      print("🗂️ 发现已缓存文件，直接播放: \(title)")
      
      // 使用本地文件播放器播放缓存文件，保留原始Navidrome信息
      let cachedFile = AudioFileInfo(
        id: file.id,
        name: file.name,
        fileName: file.fileName,
        url: cachedURL,
        fileSize: file.fileSize,
        dateAdded: file.dateAdded,
        duration: file.duration, // 保持原有的时长信息
        navidromeSongId: file.navidromeSongId,
        navidromeAlbumId: file.navidromeAlbumId,
        navidromeArtistId: file.navidromeArtistId,
        albumName: file.albumName,
        artistName: file.artistName
      )
      
      // 重置等待状态
      isWaitingForDownload = false
      pendingPlayFile = nil
      
      playLocalAudio(file: cachedFile, originalURL: file.url)
      return
    }
    
    // 文件未缓存，开始下载
    print("📥 开始下载: \(title)")
    
    // 设置当前曲目信息（用于显示）
    currentTrack = file
    
    // 获取封面（用于显示）
    extractAlbumArtwork(from: file.url)
    
    // 更新系统播放信息（显示为"准备中"状态）
    updateNowPlayingInfo()
    
    // 开始下载文件
    cacheManager.cacheAudio(trackId: cacheKey, url: file.url, priority: true)
    
    // 监听下载进度
    monitorDownloadProgress(cacheKey: cacheKey, file: file)
    
    // 智能预缓存下一首歌曲
    print("🎵 下载播放触发预缓存: cacheKey=\(cacheKey) index=\(currentTrackIndex)")
    cacheManager.smartPrefetch(playlist, currentIndex: currentTrackIndex)
  }
  
  private func monitorDownloadProgress(cacheKey: String, file: AudioFileInfo) {
    // 使用定时器监听下载进度
    Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] timer in
      guard let self = self else {
        timer.invalidate()
        return
      }
      
      // 检查是否还在等待这个文件的下载
      guard self.isWaitingForDownload,
            let pendingFile = self.pendingPlayFile,
            pendingFile.id == file.id
      else {
        timer.invalidate()
        return
      }
      
      let status = self.cacheManager.getCacheStatus(for: cacheKey)
    
      // 更新下载进度
      if let downloadTask = self.cacheManager.downloadTasks[cacheKey] {
        DispatchQueue.main.async {
          self.downloadProgress = downloadTask.progress
        }
      }
      
      switch status {
      case .cached:
        timer.invalidate()
        print("✅ 下载完成，开始播放: \(file.name)")
        
        DispatchQueue.main.async {
          self.isWaitingForDownload = false
          self.downloadProgress = 1.0
          self.pendingPlayFile = nil
          
          // 获取缓存文件URL并开始播放，保留原始Navidrome信息
          if let cachedURL = self.cacheManager.getCachedURL(for: cacheKey) {
            let cachedFile = AudioFileInfo(
              id: file.id,
              name: file.name,
              fileName: file.fileName,
              url: cachedURL,
              fileSize: file.fileSize,
              dateAdded: file.dateAdded,
              duration: file.duration, // 保持原有的时长信息
              navidromeSongId: file.navidromeSongId,
              navidromeAlbumId: file.navidromeAlbumId,
              navidromeArtistId: file.navidromeArtistId,
              albumName: file.albumName,
              artistName: file.artistName
            )
            self.playLocalAudio(file: cachedFile, originalURL: file.url)
          }
        }
        
      case .failed:
        timer.invalidate()
        print("❌ 下载失败: \(file.name)")
        
        DispatchQueue.main.async {
          self.isWaitingForDownload = false
          self.downloadProgress = 0.0
          self.pendingPlayFile = nil
          // 可以在这里添加错误处理，比如显示错误消息或跳到下一首
        }
        
      case .downloading:
        // 继续等待
        break
        
      case .notCached:
        // 如果状态变回未缓存，可能是下载被取消了
        timer.invalidate()
        print("⚠️ 下载被取消: \(file.name)")
        
        DispatchQueue.main.async {
          self.isWaitingForDownload = false
          self.downloadProgress = 0.0
          self.pendingPlayFile = nil
        }
      }
    }
  }
  
  // 原有的缓冲和下载优化代码已删除
  
  // 缓冲和下载管理相关代码已删除
  
  // 预加载和清理相关代码已删除
  
  private func playLocalAudio(file: AudioFileInfo, originalURL: URL? = nil) {
    do {
      // 创建新的音频播放器
      audioPlayer = try AVAudioPlayer(contentsOf: file.url)
      audioPlayer?.delegate = self
      audioPlayer?.volume = Float(volume)
      audioPlayer?.prepareToPlay()
            
      // 更新当前曲目信息
      currentTrack = file
      // 优先使用队列中保存的时长，如果没有则从播放器获取
      if file.duration > 0 {
        duration = file.duration
        print("✅ 使用队列中保存的时长: \(String(format: "%.1f", file.duration))秒")
      } else {
        duration = audioPlayer?.duration ?? 0
        print("⚠️ 队列中无时长信息，从播放器获取: \(String(format: "%.1f", duration))秒")
      }
            
      // 🔧 修复：先获取封面，优先使用原始URL（用于缓存文件获取Navidrome封面）
      let artworkURL = originalURL ?? file.url
      extractAlbumArtwork(from: artworkURL)
      
      // 智能预缓存：无论是缓存文件还是本地文件，都需要预缓存下一首
      let cacheKey = cacheManager.generateCacheKeyFromURL(file.url) ?? file.id.uuidString
      print("🎵 本地播放触发预缓存: cacheKey=\(cacheKey) index=\(currentTrackIndex)")
      cacheManager.smartPrefetch(playlist, currentIndex: currentTrackIndex)
            
      // 开始播放
      if audioPlayer?.play() == true {
        isPlaying = true
        startTimer()
        
        // 🔧 修复：延迟更新系统控制台，等待封面加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
          self.updateNowPlayingInfo()
        }
        
        print("开始播放: \(file.name) (播放列表位置: \(currentTrackIndex + 1)/\(playlist.count))")
      }
      
    } catch {
      print("播放音频失败: \(error.localizedDescription)")
    }
  }
  
  // 流媒体观察者相关代码已删除，因为现在只使用本地播放
  
  // 流媒体相关的时长获取代码已删除，因为现在只使用本地播放
  
  private func handlePlaybackFinished() {
    print("🏁 歌曲播放完成 - 当前索引: \(currentTrackIndex), 循环模式: \(repeatMode.displayName), 乱序: \(isShuffleEnabled)")
    
    guard !playlist.isEmpty else {
      print("❌ 播放列表为空，停止播放")
      isPlaying = false
      currentTime = 0
      stopTimer()
      updateNowPlayingInfo()
      return
    }
    
    switch repeatMode {
    case .one:
      // 单曲循环，重新播放当前歌曲
      print("🔂 单曲循环，重新播放当前歌曲")
      currentTime = 0
      playCurrentTrack()
    case .all:
      // 列表循环，播放下一首
      print("🔄 列表循环模式")
      if currentTrackIndex < playlist.count - 1 {
        currentTrackIndex += 1
        print("✅ 播放下一首: \(currentTrackIndex)")
        playCurrentTrack()
      } else {
        // 到达最后一首，回到第一首
        currentTrackIndex = 0
        print("🔄 列表循环，回到第一首: \(currentTrackIndex)")
        playCurrentTrack()
      }
    case .off:
      // 不循环
      print("⏹️ 不循环模式")
      if currentTrackIndex < playlist.count - 1 {
        // 还有下一首，继续播放
        currentTrackIndex += 1
        print("▶️ 播放下一首: \(currentTrackIndex)")
        playCurrentTrack()
      } else {
        // 播放完成，停止播放
        print("▶️ 播放列表结束，停止播放")
        isPlaying = false
        currentTime = 0
        stopTimer()
        updateNowPlayingInfo()
      }
    }
    
    autoSavePlaybackState()
  }
  
  private func managePlaylist(for file: AudioFileInfo) {
    // 检查歌曲是否已经在播放列表中
    if let existingIndex = playlist.firstIndex(where: { $0.id == file.id }) {
      // 如果歌曲已在播放列表中，将其移动到第一位
      let track = playlist.remove(at: existingIndex)
      playlist.insert(track, at: 0)
      print("歌曲已在播放列表中，移动到第一位: \(file.name)")
    } else {
      // 如果歌曲不在播放列表中，将其添加到第一位
      playlist.insert(file, at: 0)
      print("歌曲添加到播放列表第一位: \(file.name)")
    }
        
    // 重置当前播放索引为0（第一首）
    currentTrackIndex = 0
  }
    
  func pause() {
    if let audioPlayer = audioPlayer {
      audioPlayer.pause()
    } else if let avPlayer = avPlayer {
      avPlayer.pause()
    }
    isPlaying = false
    stopTimer()
    updateNowPlayingInfo()
    
    print("播放已暂停")
  }
    
  func resume() {
    if let audioPlayer = audioPlayer {
      if audioPlayer.play() {
        isPlaying = true
        startTimer()
        updateNowPlayingInfo()
        print("缓存文件播放已恢复")
      }
    } else if let avPlayer = avPlayer {
      avPlayer.play()
      isPlaying = true
      startTimer()
      updateNowPlayingInfo()
      print("流媒体播放已恢复")
    }
  }
  
  func smartResume() {
    // 智能恢复播放：根据当前状态选择最合适的恢复方式
    
    // 优先检查本地文件播放器
    if let audioPlayer = audioPlayer {
      if !audioPlayer.isPlaying {
        resume()
        print("📱 从暂停状态恢复缓存文件播放")
        return
      }
    }
    
    // 检查流媒体播放器
    if let avPlayer = avPlayer {
      if avPlayer.rate == 0 {
        resume()
        print("📱 从暂停状态恢复流媒体播放")
        return
      }
    }
    
    // 如果播放器不存在或无效，需要重新创建并恢复到保存的位置
    if let currentTrack = currentTrack {
      print("📱 重新创建播放器并恢复到保存位置")
      
      // 检查是否有缓存版本，使用正确的缓存键生成方法
      let cacheKey = cacheManager.generateCacheKeyFromURL(currentTrack.url) ?? currentTrack.id.uuidString
      if let cachedURL = cacheManager.getCachedURL(for: cacheKey) {
        // 有缓存，使用本地播放器，传递原始URL用于获取封面，保留Navidrome信息
        let cachedFile = AudioFileInfo(
          id: currentTrack.id,
          name: currentTrack.name,
          fileName: currentTrack.fileName,
          url: cachedURL,
          fileSize: currentTrack.fileSize,
          dateAdded: currentTrack.dateAdded,
          duration: currentTrack.duration, // 保持原有的时长信息
          navidromeSongId: currentTrack.navidromeSongId,
          navidromeAlbumId: currentTrack.navidromeAlbumId,
          navidromeArtistId: currentTrack.navidromeArtistId,
          albumName: currentTrack.albumName,
          artistName: currentTrack.artistName
        )
        playLocalAudio(file: cachedFile, originalURL: currentTrack.url)
      } else {
        // 无缓存，恢复流媒体播放
        resumeFromSavedState()
      }
    }
  }
    
  func stop() {
    // 停止AVAudioPlayer
    audioPlayer?.stop()
    audioPlayer = nil
    
    // 停止AVPlayer并清理资源
    avPlayer?.pause()
    avPlayer?.replaceCurrentItem(with: nil)
    avPlayer = nil
    
    // 清理PlayerItem
    playerItem = nil
    
    // 清理观察者
    cancellables.removeAll()
    
    isPlaying = false
    currentTime = 0
    stopTimer()
    clearNowPlayingInfo()
    
    print("播放器已完全停止")
  }
    
  func togglePlayPause() {
    if isPlaying {
      pause()
    } else {
      smartResume()
    }
  }
  
  // MARK: - 乱序播放和循环播放控制
  
  /// 切换乱序播放模式
  func toggleShuffle() {
    print("🔀 切换乱序播放: \(isShuffleEnabled) → \(!isShuffleEnabled)")
    
    if isShuffleEnabled {
      // 从乱序切换到非乱序：恢复原始顺序
      print("🔀 禁用乱序播放，恢复原始顺序...")
      disableShuffle()
    } else {
      // 从非乱序切换到乱序：记录当前顺序并重新排列
      print("🔀 启用乱序播放，记录当前顺序...")
      enableShuffle()
    }
    
    isShuffleEnabled.toggle()
    print("✅ 乱序播放: \(isShuffleEnabled ? "开启" : "关闭")")
    autoSavePlaybackState() // 保存状态
  }
  
  /// 切换循环播放模式
  func toggleRepeatMode() {
    let oldMode = repeatMode
    
    switch repeatMode {
    case .off:
      repeatMode = .all
    case .all:
      repeatMode = .one
    case .one:
      repeatMode = .off
    }
    
    print("🔄 切换循环模式: \(oldMode.displayName) → \(repeatMode.displayName)")
    autoSavePlaybackState() // 保存状态
  }
  
  /// 启用乱序播放
  private func enableShuffle() {
    guard !playlist.isEmpty else {
      print("❌ 乱序播放启用失败：播放列表为空")
      return
    }
    
    // 步骤1：记录当前顺序（持久化保存）
    originalPlaylist = playlist
    print("📝 记录当前播放列表顺序，共 \(playlist.count) 首歌曲")
    
    // 步骤2：以当前歌曲为第一首，将队列中其他歌曲乱序填充到之后
    var newPlaylist = [AudioFileInfo]()
    
    // 将当前播放的歌曲放在第一位
    if currentTrackIndex < playlist.count {
      newPlaylist.append(playlist[currentTrackIndex])
      print("✅ 当前歌曲 '\(playlist[currentTrackIndex].name)' 已设为乱序列表第一首")
    }
    
    // 获取除当前歌曲外的其他歌曲并乱序
    var otherTracks = playlist
    if currentTrackIndex < otherTracks.count {
      otherTracks.remove(at: currentTrackIndex)
    }
    otherTracks.shuffle()
    
    // 将乱序后的其他歌曲添加到新列表
    newPlaylist.append(contentsOf: otherTracks)
    
    // 更新播放列表
    playlist = newPlaylist
    
    // 更新当前播放索引为0（因为当前歌曲现在在第一位）
    currentTrackIndex = 0
    
    print("✅ 乱序播放已启用，新播放列表顺序已生成，当前歌曲索引更新为 0")
    print("🔀 乱序后播放列表: \(playlist.map { $0.name })")
  }
  
  /// 禁用乱序播放
  private func disableShuffle() {
    guard !originalPlaylist.isEmpty else {
      print("⚠️ 没有保存的原始播放列表，无法恢复")
      return
    }
    
    // 获取当前播放的歌曲
    let currentPlayingTrack = currentTrackIndex < playlist.count ? playlist[currentTrackIndex] : nil
    
    // 恢复原始播放列表
    playlist = originalPlaylist
    print("✅ 已恢复原始播放列表，共 \(playlist.count) 首歌曲")
    
    // 在原始列表中找到当前播放歌曲的位置
    if let currentTrack = currentPlayingTrack,
       let originalIndex = playlist.firstIndex(where: { $0.id == currentTrack.id })
    {
      currentTrackIndex = originalIndex
      print("✅ 在原始列表中找到当前歌曲，索引更新为 \(currentTrackIndex)")
    } else {
      // 如果找不到当前歌曲，保持第一首
      currentTrackIndex = 0
      print("⚠️ 在原始列表中未找到当前歌曲，索引重置为 0")
    }
    
    // 清空保存的原始列表
    originalPlaylist.removeAll()
    shuffledIndices.removeAll()
    
    print("🔀 乱序播放已禁用，恢复原始播放顺序")
    print("📋 恢复后播放列表: \(playlist.map { $0.name })")
  }
    
  func seek(to time: Double) {
    let seekTime = max(0, min(time, duration)) // 确保时间在有效范围内
    
    print("🎯 开始seek操作: \(String(format: "%.1f", seekTime))秒 / 总时长: \(String(format: "%.1f", duration))秒")
    
    if let audioPlayer = audioPlayer {
      // 本地文件播放器，直接设置时间
      audioPlayer.currentTime = seekTime
      currentTime = seekTime
      updateNowPlayingInfo()
      print("✅ 本地播放器seek完成")
    } else if let avPlayer = avPlayer {
      print("🔍 检查AVPlayer状态...")
      
      if let playerItem = playerItem {
        print("📊 PlayerItem状态: \(playerItem.status.rawValue)")
        print("📊 PlayerItem时长: \(CMTimeGetSeconds(playerItem.duration))")
        
        // 更宽松的状态检查 - 只要不是失败状态就尝试seek
        if playerItem.status == .failed {
          print("❌ 播放器状态失败，无法seek")
          return
        }
      }
      
      // 记录seek前的播放状态
      wasPlayingBeforeSeek = isPlaying
      
      // 立即更新currentTime，提供即时反馈
      currentTime = seekTime
      
      // 流媒体播放器，使用异步seek，但使用精确的容错设置
      let cmTime = CMTime(seconds: seekTime, preferredTimescale: 1000)
      
      print("🎯 执行AVPlayer seek到: \(String(format: "%.1f", seekTime))秒，播放状态: \(wasPlayingBeforeSeek)")
      
      // 使用精确的seek设置，确保准确性
      avPlayer.seek(to: cmTime, toleranceBefore: .zero, toleranceAfter: .zero) { [weak self] completed in
        DispatchQueue.main.async {
          guard let self = self else { return }
          
          if completed {
            // seek成功，获取实际播放时间
            guard let avPlayer = self.avPlayer else { return }
            
            let actualTime = CMTimeGetSeconds(avPlayer.currentTime())
            if actualTime.isFinite && actualTime >= 0 {
              // 只有当实际时间与期望时间差异较大时才更新
              if abs(actualTime - seekTime) > 0.5 {
                self.currentTime = actualTime
                print("⚠️ Seek时间校正: 期望\(String(format: "%.1f", seekTime))秒 → 实际\(String(format: "%.1f", actualTime))秒")
              }
              self.updateNowPlayingInfo()
            }
            
            // 恢复播放状态 - 如果seek前是播放状态，确保seek后继续播放
            if self.wasPlayingBeforeSeek && avPlayer.rate == 0 {
              print("🔄 恢复播放状态")
              avPlayer.play()
              self.isPlaying = true
            }
            
            print("✅ AVPlayer Seek完成: \(String(format: "%.1f", actualTime))秒，播放状态已恢复")
          } else {
            print("❌ AVPlayer Seek失败")
            
            // 即使seek失败，也要确保播放状态正确
            if self.wasPlayingBeforeSeek && self.avPlayer?.rate == 0 {
              print("🔄 Seek失败但恢复播放状态")
              self.avPlayer?.play()
              self.isPlaying = true
            }
          }
        }
      }
    } else {
      // 没有播放器时，只更新时间状态
      print("⚠️ 没有播放器实例，只更新时间状态")
      currentTime = seekTime
    }
        
    // 保存播放进度
    autoSavePlaybackState()
  }
    
  func setVolume(_ newVolume: Double) {
    volume = newVolume
    
    // 更新本地文件播放器音量
    if let audioPlayer = audioPlayer {
      audioPlayer.volume = Float(newVolume)
      print("🔊 设置缓存文件播放器音量: \(String(format: "%.1f", newVolume * 100))%")
    }
    
    // 更新流媒体播放器音量
    if let avPlayer = avPlayer {
      avPlayer.volume = Float(newVolume)
      print("🔊 设置流媒体播放器音量: \(String(format: "%.1f", newVolume * 100))%")
    }
  }
    
  private func startTimer() {
    stopTimer() // 先停止现有的定时器
        
    // 主要的时间更新定时器，更高频率以提供流畅的UI更新
    timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
      if let audioPlayer = self.audioPlayer {
        // 处理缓存文件播放器
        let newTime = audioPlayer.currentTime
        if newTime != self.currentTime {
          self.currentTime = newTime
        }
        
        // 同步播放状态
        let playerIsPlaying = audioPlayer.isPlaying
        if self.isPlaying != playerIsPlaying {
          self.isPlaying = playerIsPlaying
        }
        
      } else if let avPlayer = self.avPlayer {
        // 处理流媒体播放器
        let newTime = CMTimeGetSeconds(avPlayer.currentTime())
        if newTime.isFinite && newTime != self.currentTime {
          self.currentTime = newTime
        }
        
        // 同步播放状态
        let playerIsPlaying = avPlayer.rate > 0
        if self.isPlaying != playerIsPlaying {
          self.isPlaying = playerIsPlaying
        }
      }
    }
        
    // 系统播放信息更新定时器，较低频率以减少系统负担
    Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
      guard let self = self, self.isPlaying else {
        timer.invalidate()
        return
      }
      self.updateNowPlayingInfo()
    }
        
    // 播放进度保存定时器
    Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] timer in
      guard let self = self, self.isPlaying else {
        timer.invalidate()
        return
      }
      self.autoSavePlaybackState()
    }
  }
    
  private func stopTimer() {
    timer?.invalidate()
    timer = nil
  }
}

// MARK: - Media Control Integration

extension AudioPlayerManager {
  private func setupRemoteCommandCenter() {
    let commandCenter = MPRemoteCommandCenter.shared()
        
    // 播放命令
    commandCenter.playCommand.addTarget { [weak self] _ in
      self?.smartResume()
      return .success
    }
        
    // 暂停命令
    commandCenter.pauseCommand.addTarget { [weak self] _ in
      self?.pause()
      return .success
    }
        
    // 切换播放/暂停命令
    commandCenter.togglePlayPauseCommand.addTarget { [weak self] _ in
      self?.togglePlayPause()
      return .success
    }
        
    // 下一首命令
    commandCenter.nextTrackCommand.addTarget { [weak self] _ in
      self?.playNext()
      return .success
    }
        
    // 上一首命令
    commandCenter.previousTrackCommand.addTarget { [weak self] _ in
      self?.playPrevious()
      return .success
    }
        
    // 进度控制
    commandCenter.changePlaybackPositionCommand.addTarget { [weak self] event in
      if let event = event as? MPChangePlaybackPositionCommandEvent {
        self?.seek(to: event.positionTime)
        return .success
      }
      return .commandFailed
    }
        
    // 启用需要的命令
    commandCenter.playCommand.isEnabled = true
    commandCenter.pauseCommand.isEnabled = true
    commandCenter.togglePlayPauseCommand.isEnabled = true
    commandCenter.nextTrackCommand.isEnabled = true
    commandCenter.previousTrackCommand.isEnabled = true
    commandCenter.changePlaybackPositionCommand.isEnabled = true
  }
    
  private func disableRemoteCommandCenter() {
    let commandCenter = MPRemoteCommandCenter.shared()
    commandCenter.playCommand.removeTarget(self)
    commandCenter.pauseCommand.removeTarget(self)
    commandCenter.togglePlayPauseCommand.removeTarget(self)
    commandCenter.nextTrackCommand.removeTarget(self)
    commandCenter.previousTrackCommand.removeTarget(self)
    commandCenter.changePlaybackPositionCommand.removeTarget(self)
  }
    
  private func updateNowPlayingInfo() {
    var nowPlayingInfo = [String: Any]()
        
    // 基本信息
    if let track = currentTrack {
      // 解析艺术家和标题（格式：艺术家 - 标题）
      let components = track.name.components(separatedBy: " - ")
      if components.count >= 2 {
        nowPlayingInfo[MPMediaItemPropertyArtist] = components[0]
        nowPlayingInfo[MPMediaItemPropertyTitle] = components.dropFirst().joined(separator: " - ")
      } else {
        nowPlayingInfo[MPMediaItemPropertyTitle] = track.name
        nowPlayingInfo[MPMediaItemPropertyArtist] = "未知艺术家"
      }
      // 根据播放器类型和状态设置专辑标题
      if isWaitingForDownload {
        nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = "Navidrome (下载中...)"
      } else if audioPlayer != nil {
        nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = "Navidrome (已缓存)"
      } else if avPlayer != nil {
        nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = "Navidrome (流媒体)"
      } else {
        nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = "Navidrome"
      }
    }
        
    // 播放时间信息 - 确保时间值有效
    if isWaitingForDownload {
      // 在等待下载时，显示下载进度
      nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = 100.0 // 使用100作为总进度
      nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = downloadProgress * 100.0
      nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = 0.0 // 不在播放状态
    } else {
      // 正常播放时，显示播放进度
      let validDuration = duration > 0 ? duration : 0
      let validCurrentTime = max(0, min(currentTime, validDuration))
    
      nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = validDuration
      nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = validCurrentTime
      nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlaying ? 1.0 : 0.0
    }
    
    // 添加音量信息（这有助于系统播放面板的音量控制）
    nowPlayingInfo[MPNowPlayingInfoPropertyCurrentPlaybackDate] = Date()
    nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackQueueIndex] = currentTrackIndex
    nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackQueueCount] = playlist.count
        
    // 专辑封面
    if let artwork = albumArtwork {
      let mediaArtwork = MPMediaItemArtwork(boundsSize: artwork.size) { _ in
        artwork
      }
      nowPlayingInfo[MPMediaItemPropertyArtwork] = mediaArtwork
      // print("🎨 封面已设置到系统控制中心: \(artwork.size)")
    } else {
      print("⚠️ 系统控制中心无封面")
    }
        
    // 更新到系统
    MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
  }
    
  private func clearNowPlayingInfo() {
    MPNowPlayingInfoCenter.default().nowPlayingInfo = nil
  }
}

// MARK: - AVAudioPlayerDelegate

extension AudioPlayerManager: AVAudioPlayerDelegate {
  func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
    DispatchQueue.main.async {
      if flag {
        // 播放成功完成，调用统一的播放完成处理方法
        print("🎵 AVAudioPlayer播放完成，调用handlePlaybackFinished")
        self.handlePlaybackFinished()
      } else {
        // 播放失败或被中断，停止播放
        print("❌ AVAudioPlayer播放失败或被中断")
        self.isPlaying = false
        self.currentTime = 0
        self.stopTimer()
        self.updateNowPlayingInfo()
      }
    }
  }
    
  func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
    DispatchQueue.main.async {
      self.isPlaying = false
      print("音频解码错误: \(error?.localizedDescription ?? "未知错误")")
    }
  }
}

// MARK: - Album Artwork Extraction

extension AudioPlayerManager {
  // 安全的字节读取辅助方法
  private func readUInt32BigEndian(from data: Data, at offset: Int) -> UInt32? {
    guard offset + 4 <= data.count else { return nil }
    let bytes = Array(data[offset..<(offset + 4)])
    return (UInt32(bytes[0]) << 24) |
      (UInt32(bytes[1]) << 16) |
      (UInt32(bytes[2]) << 8) |
      UInt32(bytes[3])
  }
    
  private func readUInt32LittleEndian(from data: Data, at offset: Int) -> UInt32? {
    guard offset + 4 <= data.count else { return nil }
    let bytes = Array(data[offset..<(offset + 4)])
    return UInt32(bytes[0]) |
      (UInt32(bytes[1]) << 8) |
      (UInt32(bytes[2]) << 16) |
      (UInt32(bytes[3]) << 24)
  }
    
  private func readUInt16BigEndian(from data: Data, at offset: Int) -> UInt16? {
    guard offset + 2 <= data.count else { return nil }
    let bytes = Array(data[offset..<(offset + 2)])
    return (UInt16(bytes[0]) << 8) | UInt16(bytes[1])
  }
    
  private func readUInt16LittleEndian(from data: Data, at offset: Int) -> UInt16? {
    guard offset + 2 <= data.count else { return nil }
    let bytes = Array(data[offset..<(offset + 2)])
    return UInt16(bytes[0]) | (UInt16(bytes[1]) << 8)
  }

  private func extractAlbumArtwork(from url: URL) {
    let cacheKey = url.absoluteString
        
    // 首先检查缓存
    if let cachedArtwork = artworkCache[cacheKey] {
      DispatchQueue.main.async {
        self.albumArtwork = cachedArtwork
        self.updateNowPlayingInfo()
      }
      return
    }
    
    // 🔧 关键修改：优先尝试从Navidrome API获取封面
    // 1. 检查是否为Navidrome流媒体URL
    if url.scheme == "http" || url.scheme == "https" {
      if isNavidromeStream(url) {
        loadNavidromeCoverArt(from: url)
        return
      } else {
        DispatchQueue.main.async {
          self.albumArtwork = nil
          self.updateNowPlayingInfo()
        }
        return
      }
    }
    
    // 2. 对于缓存文件，从当前曲目信息获取封面
    if url.path.contains("AudioCache") {
      print("🔍 处理缓存文件: \(url.lastPathComponent)")
      
      // 直接从当前曲目获取歌曲信息
      if let currentTrack = currentTrack {
        if let songId = extractSongId(from: currentTrack.url) {
          print("✅ 从当前曲目URL提取到歌曲ID: \(songId)")
          loadNavidromeCoverArtBySongId(songId)
          return
        } else if isNavidromeStream(currentTrack.url) {
          print("✅ 使用当前曲目URL获取封面")
          loadNavidromeCoverArt(from: currentTrack.url)
          return
        }
      }
      
      print("❌ 无法从当前曲目获取Navidrome信息")
    }
    
    // 3. 对于其他本地文件，不获取封面（按照您的要求）
    DispatchQueue.main.async {
      self.albumArtwork = nil
      self.updateNowPlayingInfo()
    }
  }
  
  /// 从缓存文件路径推断原始URL
  private func getOriginalURLFromCacheFile(_ cacheURL: URL) -> URL? {
    let cacheFileName = cacheURL.lastPathComponent
    print("🔍 推断原始URL - 缓存文件: \(cacheFileName)")
    
    // 从当前播放列表中查找对应的原始文件
    if let currentTrack = currentTrack {
      let cacheKey = cacheManager.generateCacheKeyFromURL(currentTrack.url) ?? currentTrack.id.uuidString
      let expectedCacheFileName = "cache_\(cacheKey)"
      
      print("   当前曲目: \(currentTrack.name)")
      print("   期望文件名: \(expectedCacheFileName)")
      print("   匹配结果: \(cacheFileName.contains(expectedCacheFileName))")
      
      if cacheFileName.contains(expectedCacheFileName) {
        print("✅ 通过当前曲目匹配成功")
        return currentTrack.url
      }
    }
    
    // 如果无法从当前曲目匹配，尝试从播放列表中查找
    print("🔍 在播放列表中查找...")
    for (index, track) in playlist.enumerated() {
      let cacheKey = cacheManager.generateCacheKeyFromURL(track.url) ?? track.id.uuidString
      let expectedCacheFileName = "cache_\(cacheKey)"
      
      if cacheFileName.contains(expectedCacheFileName) {
        print("✅ 在播放列表[\(index)]找到匹配: \(track.name)")
        return track.url
      }
    }
    
    print("❌ 未找到匹配的原始URL")
    return nil
  }
  
  // MARK: - Navidrome封面获取方法
  
  private func isNavidromeStream(_ url: URL) -> Bool {
    let urlString = url.absoluteString
    return (urlString.contains("/rest/stream") || urlString.contains("navidrome")) &&
      (url.scheme == "http" || url.scheme == "https")
  }
  
  private func loadNavidromeCoverArt(from url: URL) {
    // 提取歌曲ID
    guard let songId = extractSongId(from: url) else {
      print("❌ 无法提取歌曲ID")
      DispatchQueue.main.async {
        self.albumArtwork = nil
        self.updateNowPlayingInfo()
      }
      return
    }
    
    let cacheKey = url.absoluteString
    
    // 检查缓存
    if let cachedArtwork = artworkCache[cacheKey] {
      DispatchQueue.main.async {
        self.albumArtwork = cachedArtwork
        self.updateNowPlayingInfo()
      }
      return
    }
    
    // 清除旧封面
    DispatchQueue.main.async {
      self.albumArtwork = nil
    }
    
    // 确保有可用的Navidrome客户端
    if navidromeClient == nil || !navidromeClient!.isAuthenticated {
      let retryKey = "navidrome_retry_\(songId)"
      if UserDefaults.standard.bool(forKey: retryKey) {
        DispatchQueue.main.async {
          self.albumArtwork = nil
          self.updateNowPlayingInfo()
        }
        return
      }
      
      UserDefaults.standard.set(true, forKey: retryKey)
      setupNavidromeClient()
      
      DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
        UserDefaults.standard.removeObject(forKey: retryKey)
        self?.loadNavidromeCoverArt(from: url)
      }
      return
    }
    
    guard let client = navidromeClient else {
      print("❌ Navidrome客户端初始化失败")
      DispatchQueue.main.async {
        self.albumArtwork = nil
        self.updateNowPlayingInfo()
      }
      return
    }
    
    // 获取封面URL
    guard let coverURL = client.getCoverArtURL(for: songId, size: 300) else {
      print("❌ 无法获取封面URL")
      DispatchQueue.main.async {
        self.albumArtwork = nil
        self.updateNowPlayingInfo()
      }
      return
    }
    
    Task {
      do {
        let (data, response) = try await URLSession.shared.data(from: coverURL)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200,
              let image = UIImage(data: data)
        else {
          print("❌ 封面下载失败")
          await MainActor.run {
            self.albumArtwork = nil
            self.updateNowPlayingInfo()
          }
          return
        }
        
        await MainActor.run {
          self.artworkCache[cacheKey] = image
          self.albumArtwork = image
          self.updateNowPlayingInfo()
          print("✅ 封面加载成功: \(image.size), 数据大小: \(data.count) bytes")
        }
        
      } catch {
        await MainActor.run {
          self.albumArtwork = nil
          self.updateNowPlayingInfo()
        }
        print("❌ 封面加载失败: \(error.localizedDescription)")
      }
    }
  }
  
  private func extractSongId(from url: URL) -> String? {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    // 查找id参数
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }
  
  /// 直接通过歌曲ID获取Navidrome封面
  private func loadNavidromeCoverArtBySongId(_ songId: String) {
    let cacheKey = "songId_\(songId)"
    
    // 检查缓存
    if let cachedArtwork = artworkCache[cacheKey] {
      DispatchQueue.main.async {
        self.albumArtwork = cachedArtwork
        self.updateNowPlayingInfo()
      }
      return
    }
    
    // 清除旧封面
    DispatchQueue.main.async {
      self.albumArtwork = nil
    }
    
    // 确保有可用的Navidrome客户端
    if navidromeClient == nil || !navidromeClient!.isAuthenticated {
      let retryKey = "navidrome_retry_\(songId)"
      if UserDefaults.standard.bool(forKey: retryKey) {
        DispatchQueue.main.async {
          self.albumArtwork = nil
          self.updateNowPlayingInfo()
        }
        return
      }
      
      UserDefaults.standard.set(true, forKey: retryKey)
      setupNavidromeClient()
      
      DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
        UserDefaults.standard.removeObject(forKey: retryKey)
        self?.loadNavidromeCoverArtBySongId(songId)
      }
      return
    }
    
    guard let client = navidromeClient else {
      print("❌ Navidrome客户端初始化失败")
      DispatchQueue.main.async {
        self.albumArtwork = nil
        self.updateNowPlayingInfo()
      }
      return
    }
    
    // 获取封面URL
    guard let coverURL = client.getCoverArtURL(for: songId, size: 300) else {
      print("❌ 无法获取封面URL")
      DispatchQueue.main.async {
        self.albumArtwork = nil
        self.updateNowPlayingInfo()
      }
      return
    }
    
    Task {
      do {
        let (data, response) = try await URLSession.shared.data(from: coverURL)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200,
              let image = UIImage(data: data)
        else {
          print("❌ 封面下载失败")
          await MainActor.run {
            self.albumArtwork = nil
            self.updateNowPlayingInfo()
          }
          return
        }
        
        await MainActor.run {
          self.artworkCache[cacheKey] = image
          self.albumArtwork = image
          self.updateNowPlayingInfo()
          print("✅ 封面加载成功: \(image.size), 数据大小: \(data.count) bytes")
        }
        
      } catch {
        await MainActor.run {
          self.albumArtwork = nil
          self.updateNowPlayingInfo()
        }
        print("❌ 封面加载失败: \(error.localizedDescription)")
      }
    }
  }
  
  /// 强制刷新系统播放控制台信息
  func forceRefreshNowPlayingInfo() {
    updateNowPlayingInfo()
  }
  
  // MARK: - 原有方法保持不变

  private func extractArtworkFromAVAsset(url: URL) async -> UIImage? {
    // 确保只处理本地文件，避免网络下载
    if url.scheme == "http" || url.scheme == "https" {
      print("⚠️ extractArtworkFromAVAsset 跳过网络URL")
      return nil
    }
    
    let asset = AVURLAsset(url: url)
    
    do {
      let metadataItems = try await asset.load(.metadata)
      
      // 尝试多种可能的封面键
      let artworkKeys = ["artwork", "cover", "picture", "APIC"]
          
      for item in metadataItems {
        // 检查commonKey
        if let commonKey = item.commonKey?.rawValue,
           artworkKeys.contains(commonKey.lowercased())
        {
          do {
            if let data = try await item.load(.dataValue),
               let image = UIImage(data: data)
            {
              print("从AVAsset commonKey获取到封面: \(commonKey)")
              return image
            }
          } catch {
            // 忽略单个item的加载错误，继续处理下一个
          }
        }
              
        // 检查keySpace和key的组合
        if let keySpace = item.keySpace?.rawValue,
           let key = item.key as? String
        {
          let fullKey = "\(keySpace):\(key)"
          print("检查元数据键: \(fullKey)")
                  
          // iTunes/MP4格式
          if keySpace.contains("itsk") || keySpace.contains("iTunes") {
            if key.lowercased().contains("covr") || key.lowercased().contains("artwork") {
              do {
                if let data = try await item.load(.dataValue),
                   let image = UIImage(data: data)
                {
                  print("从iTunes元数据获取到封面")
                  return image
                }
              } catch {
                // 忽略单个item的加载错误，继续处理下一个
              }
            }
          }
                  
          // ID3格式
          if keySpace.contains("ID3") {
            if key.contains("APIC") || key.lowercased().contains("picture") {
              do {
                if let data = try await item.load(.dataValue),
                   let image = UIImage(data: data)
                {
                  print("从ID3标签获取到封面")
                  return image
                }
              } catch {
                // 忽略单个item的加载错误，继续处理下一个
              }
            }
          }
        }
              
        // 直接检查dataValue是否为图片数据
        do {
          if let data = try await item.load(.dataValue),
             data.count > 1000, // 过滤掉太小的数据
             let image = UIImage(data: data)
          {
            print("从通用数据获取到封面")
            return image
          }
        } catch {
          // 忽略单个item的加载错误，继续处理下一个
        }
      }
      
      return nil
    } catch {
      print("加载AVAsset元数据失败: \(error.localizedDescription)")
      return nil
    }
  }
    
  // 方法2：针对特定文件格式的手动解析（仅适用于本地文件）
  private func extractArtworkFromFileFormat(url: URL) -> UIImage? {
    // 检查是否为网络URL，如果是则跳过
    if url.scheme == "http" || url.scheme == "https" {
      return nil
    }
    
    let fileExtension = url.pathExtension.lowercased()
        
    switch fileExtension {
    case "flac":
      return extractFLACArtwork(url: url)
    case "wav":
      return extractWAVArtwork(url: url)
    case "aiff", "aif":
      return extractAIFFArtwork(url: url)
    case "mp3":
      return extractMP3Artwork(url: url)
    case "m4a", "aac", "m4p", "m4r":
      return extractM4AArtwork(url: url)
    case "ogg", "oga":
      return extractOGGArtwork(url: url)
    case "wma":
      return extractWMAArtwork(url: url)
    default:
      return nil
    }
  }
    
  // FLAC格式封面提取
  private func extractFLACArtwork(url: URL) -> UIImage? {
    guard let data = try? Data(contentsOf: url) else { return nil }
        
    // FLAC文件结构：4字节"fLaC" + 元数据块
    guard data.count > 4,
          String(data: data.prefix(4), encoding: .ascii) == "fLaC"
    else {
      return nil
    }
        
    var offset = 4
        
    // 解析元数据块
    while offset < data.count - 4 {
      // 安全地读取4字节块头
      guard let blockHeader = readUInt32BigEndian(from: data, at: offset) else { break }
            
      let isLast = (blockHeader & 0x80000000) != 0
      let blockType = (blockHeader & 0x7F000000) >> 24
      let blockLength = Int(blockHeader & 0x00FFFFFF)
            
      offset += 4
            
      // 类型6是PICTURE块
      if blockType == 6, offset + blockLength <= data.count {
        let pictureData = data.subdata(in: offset..<(offset + blockLength))
        if let artwork = parseFLACPictureBlock(pictureData) {
          print("从FLAC PICTURE块获取到封面")
          return artwork
        }
      }
            
      offset += blockLength
            
      if isLast { break }
    }
        
    return nil
  }
    
  // 解析FLAC PICTURE块
  private func parseFLACPictureBlock(_ data: Data) -> UIImage? {
    guard data.count > 32 else { return nil }
        
    var offset = 0
        
    // 跳过picture type (4字节)
    offset += 4
        
    // 安全读取MIME type长度
    guard let mimeLength32 = readUInt32BigEndian(from: data, at: offset) else { return nil }
    let mimeLength = Int(mimeLength32)
    offset += 4 + mimeLength
        
    // 安全读取描述长度
    guard let descLength32 = readUInt32BigEndian(from: data, at: offset) else { return nil }
    let descLength = Int(descLength32)
    offset += 4 + descLength
        
    // 跳过宽度、高度、颜色深度、颜色数量 (16字节)
    offset += 16
        
    // 安全读取图片数据长度
    guard let imageLength32 = readUInt32BigEndian(from: data, at: offset) else { return nil }
    let imageLength = Int(imageLength32)
    offset += 4
        
    // 提取图片数据
    guard offset + imageLength <= data.count else { return nil }
    let imageData = data.subdata(in: offset..<(offset + imageLength))
        
    return UIImage(data: imageData)
  }
    
  // WAV格式封面提取（通常在INFO块中）
  private func extractWAVArtwork(url: URL) -> UIImage? {
    guard let data = try? Data(contentsOf: url) else { return nil }
        
    // 检查RIFF文件头
    guard data.count > 12,
          String(data: data[0..<4], encoding: .ascii) == "RIFF",
          String(data: data[8..<12], encoding: .ascii) == "WAVE"
    else {
      return nil
    }
        
    var offset = 12
        
    // 查找INFO或LIST块中的封面信息
    while offset + 8 < data.count {
      let chunkId = String(data: data[offset..<(offset + 4)], encoding: .ascii) ?? ""
            
      // 安全读取chunk大小（小端序）
      guard let chunkSize32 = readUInt32LittleEndian(from: data, at: offset + 4) else { break }
      let chunkSize = Int(chunkSize32)
            
      if chunkId == "LIST" || chunkId == "INFO" {
        // 在这些块中查找图片数据
        // WAV文件中的封面通常以非标准方式存储
        // 大多数情况下依赖文件夹图片
        break
      }
            
      offset += 8 + chunkSize
      if chunkSize % 2 != 0 { offset += 1 } // 对齐到偶数字节
    }
        
    return nil
  }
    
  // AIFF格式封面提取
  private func extractAIFFArtwork(url: URL) -> UIImage? {
    guard let data = try? Data(contentsOf: url) else { return nil }
        
    // 检查AIFF文件头
    guard data.count > 12,
          String(data: data[0..<4], encoding: .ascii) == "FORM",
          String(data: data[8..<12], encoding: .ascii) == "AIFF"
    else {
      return nil
    }
        
    var offset = 12
        
    // 查找包含封面的块
    while offset + 8 < data.count {
//      let chunkId = String(data: data[offset..<(offset + 4)], encoding: .ascii) ?? ""
            
      // 安全读取chunk大小（大端序）
      guard let chunkSize32 = readUInt32BigEndian(from: data, at: offset + 4) else { break }
      let chunkSize = Int(chunkSize32)
            
      // AIFF很少有内嵌封面，主要依赖文件夹图片
      offset += 8 + chunkSize
      if chunkSize % 2 != 0 { offset += 1 }
    }
        
    return nil
  }
    
  // OGG格式封面提取
  private func extractOGGArtwork(url: URL) -> UIImage? {
    guard let data = try? Data(contentsOf: url) else { return nil }
        
    // 检查OGG文件头
    guard data.count > 4,
          String(data: data[0..<4], encoding: .ascii) == "OggS"
    else {
      return nil
    }
        
    // OGG/Vorbis注释中的封面通常使用METADATA_BLOCK_PICTURE
    // 这是一个复杂的解析过程，通常需要专门的库
    // 这里只做基本结构检查
    return nil
  }
    
  // WMA格式封面提取
  private func extractWMAArtwork(url: URL) -> UIImage? {
//    guard let data = try? Data(contentsOf: url) else { return nil }
        
    // WMA文件使用ASF格式，有复杂的头部结构
    // 通常AVAsset已经能处理WMA的元数据
    // 这里主要作为备选方案
    return nil
  }
    
  // MP3格式封面提取（ID3v2）
  private func extractMP3Artwork(url: URL) -> UIImage? {
    guard let data = try? Data(contentsOf: url) else { return nil }
        
    // 检查ID3v2标签
    guard data.count > 10,
          String(data: data.prefix(3), encoding: .ascii) == "ID3"
    else {
      return nil
    }
        
    // 读取ID3v2头部信息
    let version = data[3]
//    let flags = data[5]
        
    // 计算标签大小（同步安全整数）
    let sizeBytes = Array(data[6..<10])
    let tagSize = (Int(sizeBytes[0]) << 21) |
      (Int(sizeBytes[1]) << 14) |
      (Int(sizeBytes[2]) << 7) |
      Int(sizeBytes[3])
        
    var offset = 10
        
    // 解析帧
    while offset < min(tagSize + 10, data.count - 10) {
      let frameId = String(data: data[offset..<(offset + 4)], encoding: .ascii) ?? ""
      offset += 4
            
      if frameId == "APIC" { // Attached Picture
        // 安全读取帧大小
        guard offset + 4 <= data.count else { break }
        let frameSizeBytes = Array(data[offset..<(offset + 4)])
        let frameSize: Int
                
        if version >= 4 {
          // ID3v2.4使用同步安全整数
          frameSize = (Int(frameSizeBytes[0]) << 21) |
            (Int(frameSizeBytes[1]) << 14) |
            (Int(frameSizeBytes[2]) << 7) |
            Int(frameSizeBytes[3])
        } else {
          // ID3v2.3使用普通整数
          frameSize = (Int(frameSizeBytes[0]) << 24) |
            (Int(frameSizeBytes[1]) << 16) |
            (Int(frameSizeBytes[2]) << 8) |
            Int(frameSizeBytes[3])
        }
                
        offset += 6 // 跳过大小和标志
                
        // 解析APIC帧内容
        if let artwork = parseID3APICFrame(data: data, offset: offset, frameSize: frameSize) {
          print("从MP3 ID3标签获取到封面")
          return artwork
        }
        break
      } else {
        // 跳过其他帧
        guard offset + 4 <= data.count else { break }
        let frameSizeBytes = Array(data[offset..<(offset + 4)])
        let frameSize = version >= 4 ?
          ((Int(frameSizeBytes[0]) << 21) | (Int(frameSizeBytes[1]) << 14) | (Int(frameSizeBytes[2]) << 7) | Int(frameSizeBytes[3])) :
          ((Int(frameSizeBytes[0]) << 24) | (Int(frameSizeBytes[1]) << 16) | (Int(frameSizeBytes[2]) << 8) | Int(frameSizeBytes[3]))
                
        offset += 10 + frameSize
      }
    }
        
    return nil
  }
    
  // 解析ID3 APIC帧
  private func parseID3APICFrame(data: Data, offset: Int, frameSize: Int) -> UIImage? {
    guard offset + frameSize <= data.count else { return nil }
        
    var pos = offset
        
    // 跳过编码字节
    pos += 1
        
    // 读取MIME类型（以null结尾）
    while pos < data.count, data[pos] != 0 {
      pos += 1
    }
    pos += 1 // 跳过null终止符
        
    // 跳过图片类型
    pos += 1
        
    // 跳过描述（以null结尾）
    while pos < data.count, data[pos] != 0 {
      pos += 1
    }
    pos += 1 // 跳过null终止符
        
    // 剩余的就是图片数据
    let imageData = data.subdata(in: pos..<(offset + frameSize))
    return UIImage(data: imageData)
  }
    
  // M4A格式封面提取
  private func extractM4AArtwork(url: URL) -> UIImage? {
    // M4A格式通常通过AVAsset就能获取到封面
    // 这里做额外的atom解析作为备选方案
    return nil
  }
    
  // 方法3：查找文件夹内的封面图片（仅适用于本地文件）
  private func searchForFolderArtwork(url: URL) -> UIImage? {
    // 检查是否为网络URL，如果是则跳过
    if url.scheme == "http" || url.scheme == "https" {
      return nil
    }
    
    let directory = url.deletingLastPathComponent()
        
    // 常见的封面文件名
    let coverNames = [
      "cover", "folder", "front", "album", "albumart",
      "Cover", "Folder", "Front", "Album", "AlbumArt",
      "COVER", "FOLDER", "FRONT", "ALBUM", "ALBUMART"
    ]
        
    // 常见的图片扩展名
    let imageExtensions = ["jpg", "jpeg", "png", "bmp", "tiff", "webp"]
        
    // 首先查找与音频文件同名的图片
    let audioFileName = url.deletingPathExtension().lastPathComponent
    for ext in imageExtensions {
      let imageUrl = directory.appendingPathComponent("\(audioFileName).\(ext)")
      if FileManager.default.fileExists(atPath: imageUrl.path),
         let image = UIImage(contentsOfFile: imageUrl.path)
      {
        print("找到同名封面文件: \(imageUrl.lastPathComponent)")
        return image
      }
    }
        
    // 然后查找常见的封面文件名
    for name in coverNames {
      for ext in imageExtensions {
        let imageUrl = directory.appendingPathComponent("\(name).\(ext)")
        if FileManager.default.fileExists(atPath: imageUrl.path),
           let image = UIImage(contentsOfFile: imageUrl.path)
        {
          print("找到文件夹封面: \(imageUrl.lastPathComponent)")
          return image
        }
      }
    }
        
    // 最后查找目录中的第一个图片文件
    do {
      let contents = try FileManager.default.contentsOfDirectory(at: directory, includingPropertiesForKeys: nil)
      for file in contents {
        if imageExtensions.contains(file.pathExtension.lowercased()),
           let image = UIImage(contentsOfFile: file.path)
        {
          print("找到目录中的图片文件: \(file.lastPathComponent)")
          return image
        }
      }
    } catch {
      print("无法读取本地目录内容: \(error)")
    }
        
    return nil
  }
}

// MARK: - System Volume Control

extension AudioPlayerManager {
  private func setupSystemVolumeObserver() {
    // 使用KVO监听系统音量变化
    do {
      try AVAudioSession.sharedInstance().setActive(true)
      AVAudioSession.sharedInstance().addObserver(
        self,
        forKeyPath: "outputVolume",
        options: [.new, .old],
        context: nil
      )
      print("✅ 系统音量监听器设置成功")
    } catch {
      print("❌ 系统音量监听器设置失败: \(error.localizedDescription)")
    }
  }
    
  private func syncSystemVolume() {
    let audioSession = AVAudioSession.sharedInstance()
    volume = Double(audioSession.outputVolume)
  }
  
  /// 调试音量控制状态
  func debugPlaybackModes() {
    print("🐛 调试播放模式状态:")
    print("  - 乱序播放: \(isShuffleEnabled)")
    print("  - 循环模式: \(repeatMode.displayName)")
    print("  - 播放列表长度: \(playlist.count)")
    print("  - 当前索引: \(currentTrackIndex)")
    print("  - 原始播放列表长度: \(originalPlaylist.count)")
    print("  - 播放列表歌曲: \(playlist.map { $0.name })")
    
    if let currentTrack = currentTrack {
      print("  - 当前歌曲: \(currentTrack.name)")
    } else {
      print("  - 当前歌曲: 无")
    }
  }
  
  func debugVolumeControl() -> String {
    let audioSession = AVAudioSession.sharedInstance()
    var debug = "🔊 音量控制调试信息:\n"
    debug += "系统音量: \(Int(audioSession.outputVolume * 100))%\n"
    debug += "应用音量: \(Int(volume * 100))%\n"
    debug += "AVAudioPlayer音量: \(audioPlayer?.volume ?? 0.0)\n"
    debug += "AVPlayer音量: \(avPlayer?.volume ?? 0.0)\n"
    debug += "音频会话类别: \(audioSession.category.rawValue)\n"
    debug += "音频会话选项: \(audioSession.categoryOptions.rawValue)\n"
    debug += "音频会话激活: \(audioSession.isOtherAudioPlaying ? "是" : "否")\n"
    
    return debug
  }
    
  override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey: Any]?, context: UnsafeMutableRawPointer?) {
    if keyPath == "outputVolume" {
      DispatchQueue.main.async {
        let oldVolume = self.volume
        self.syncSystemVolume()
        let newVolume = self.volume
        
        if abs(oldVolume - newVolume) > 0.01 { // 避免微小变化的频繁更新
          print("🔊 系统音量变化: \(Int(oldVolume * 100))% → \(Int(newVolume * 100))%")
          
          // 同步到音频播放器
          self.audioPlayer?.volume = Float(newVolume)
          self.avPlayer?.volume = Float(newVolume)
          
          // 更新系统播放信息
          self.updateNowPlayingInfo()
        }
      }
    }
  }
    
  func setSystemVolume(_ newVolume: Double) {
    let clampedVolume = max(0.0, min(1.0, newVolume))
    volume = clampedVolume
    
    // 更新音频播放器音量
    audioPlayer?.volume = Float(clampedVolume)
    avPlayer?.volume = Float(clampedVolume)
    
    print("🔊 设置音量: \(Int(clampedVolume * 100))%")
        
    // 使用MPVolumeView来控制系统音量
    DispatchQueue.main.async {
      let volumeView = MPVolumeView(frame: CGRect.zero)
      volumeView.showsVolumeSlider = false
      volumeView.showsRouteButton = false
      
      // 添加到窗口以确保可以访问滑块
      if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
         let window = windowScene.windows.first
      {
        window.addSubview(volumeView)
        
        // 查找音量滑块并设置值
        for subview in volumeView.subviews {
          if let slider = subview as? UISlider {
            slider.value = Float(clampedVolume)
            print("✅ 系统音量已设置: \(Int(clampedVolume * 100))%")
            break
          }
        }
        
        // 移除临时添加的视图
        volumeView.removeFromSuperview()
      }
    }
    
    // 更新系统播放信息中的音量
    updateNowPlayingInfo()
  }
    
  // MARK: - Playlist Control

  func playNext() {
    guard !playlist.isEmpty else {
      print("❌ playNext: 播放列表为空")
      return
    }
    
    let oldIndex = currentTrackIndex
    
    // 无论是否乱序，都按当前播放列表的顺序播放下一首
    print("▶️ 播放下一首 - 当前索引: \(currentTrackIndex), 循环模式: \(repeatMode.displayName), 乱序: \(isShuffleEnabled)")
    
    // 首先检查循环模式
    switch repeatMode {
    case .one:
      // 单曲循环，无论何时都重新播放当前歌曲
      print("🔂 单曲循环模式，重新播放当前歌曲")
      currentTime = 0
      playCurrentTrack()
    case .all, .off:
      // 列表循环或不循环模式，正常处理下一首
      if currentTrackIndex < playlist.count - 1 {
        // 还有下一首歌，直接播放
        currentTrackIndex += 1
        print("✅ 播放下一首: \(oldIndex) → \(currentTrackIndex)")
        playCurrentTrack()
      } else {
        // 到达最后一首，根据循环模式处理
        if repeatMode == .all {
          // 列表循环，回到第一首
          currentTrackIndex = 0
          print("🔄 列表循环: \(oldIndex) → \(currentTrackIndex)")
          playCurrentTrack()
        } else {
          // 不循环，停止播放
          print("⏹️ 不循环模式，已到达列表末尾，停止播放")
          isPlaying = false
          currentTime = 0
          stopTimer()
          updateNowPlayingInfo()
        }
      }
    }
    
    autoSavePlaybackState()
  }
    
  func playPrevious() {
    guard !playlist.isEmpty else {
      print("❌ playPrevious: 播放列表为空")
      return
    }
    
    let oldIndex = currentTrackIndex
    
    // 无论是否乱序，都按当前播放列表的顺序播放上一首
    print("◀️ 播放上一首 - 当前索引: \(currentTrackIndex), 循环模式: \(repeatMode.displayName), 乱序: \(isShuffleEnabled)")
    
    // 首先检查循环模式
    switch repeatMode {
    case .one:
      // 单曲循环，无论何时都重新播放当前歌曲
      print("🔂 单曲循环模式，重新播放当前歌曲")
      currentTime = 0
      playCurrentTrack()
    case .all, .off:
      // 列表循环或不循环模式，正常处理上一首
      if currentTrackIndex > 0 {
        // 还有上一首歌，直接播放
        currentTrackIndex -= 1
        print("✅ 播放上一首: \(oldIndex) → \(currentTrackIndex)")
        playCurrentTrack()
      } else {
        // 到达第一首，根据循环模式处理
        if repeatMode == .all {
          // 列表循环，跳到最后一首
          currentTrackIndex = playlist.count - 1
          print("🔄 列表循环到最后一首: \(oldIndex) → \(currentTrackIndex)")
          playCurrentTrack()
        } else {
          // 不循环，跳到最后一首（传统行为）
          currentTrackIndex = playlist.count - 1
          print("⏮️ 不循环模式，跳到最后一首: \(oldIndex) → \(currentTrackIndex)")
          playCurrentTrack()
        }
      }
    }
    
    autoSavePlaybackState()
  }
    
  func removeFromPlaylist(at index: Int) {
    guard index < playlist.count else { return }
        
    let removedTrack = playlist[index]
    playlist.remove(at: index)
        
    // 如果删除的是当前播放的歌曲
    if index == currentTrackIndex {
      if playlist.isEmpty {
        stop()
        currentTrackIndex = 0
      } else {
        // 调整索引并播放下一首
        if currentTrackIndex >= playlist.count {
          currentTrackIndex = 0
        }
        playCurrentTrack()
      }
    } else if index < currentTrackIndex {
      // 如果删除的是当前歌曲之前的歌曲，需要调整索引
      currentTrackIndex -= 1
    }
        
    print("从播放列表删除: \(removedTrack.name)")
    autoSavePlaybackState()
  }
    
  func clearPlaylist() {
    stop()
    playlist.removeAll()
    originalPlaylist.removeAll()
    shuffledIndices.removeAll()
    currentTrackIndex = 0
    print("播放列表已清空")
    autoSavePlaybackState()
  }
    
  func addToPlaylist(_ file: AudioFileInfo) {
    // 检查歌曲是否已在播放列表中
    if !playlist.contains(where: { $0.id == file.id }) {
      playlist.append(file)
      print("歌曲添加到播放列表末尾: \(file.name)")
      autoSavePlaybackState()
    } else {
      print("歌曲已在播放列表中: \(file.name)")
    }
  }
  
  func replacePlaylist(with newPlaylist: [AudioFileInfo], startFromIndex: Int = 0) {
    // 停止当前播放
    stop()
    
    // 在乱序状态下播放新歌曲时，自动恢复为非乱序状态
    if isShuffleEnabled {
      print("🔀 检测到在乱序状态下播放新歌曲，自动恢复为非乱序状态")
      isShuffleEnabled = false
      originalPlaylist.removeAll()
      shuffledIndices.removeAll()
    }
    
    // 替换播放列表
    playlist = newPlaylist
    currentTrackIndex = min(startFromIndex, max(0, newPlaylist.count - 1))
    
    print("播放列表已替换: \(newPlaylist.count)首歌曲，从第\(currentTrackIndex + 1)首开始")
    
    // 重置缓存管理器的播放索引，确保新播放列表能正确触发缓存
    cacheManager.resetPlayingTrackIndex()
    
    // 如果有歌曲，开始播放第一首
    if !playlist.isEmpty {
      playCurrentTrack()
    }
    
    autoSavePlaybackState()
  }
    
  func moveTrack(from sourceIndex: Int, to destinationIndex: Int) {
    guard sourceIndex < playlist.count, destinationIndex < playlist.count else { return }
        
    let track = playlist.remove(at: sourceIndex)
    playlist.insert(track, at: destinationIndex)
        
    // 更新当前播放索引
    if sourceIndex == currentTrackIndex {
      currentTrackIndex = destinationIndex
    } else if sourceIndex < currentTrackIndex, destinationIndex >= currentTrackIndex {
      currentTrackIndex -= 1
    } else if sourceIndex > currentTrackIndex, destinationIndex <= currentTrackIndex {
      currentTrackIndex += 1
    }
        
    print("歌曲移动: \(track.name) 从位置\(sourceIndex + 1)到位置\(destinationIndex + 1)")
    autoSavePlaybackState()
  }
}

// MARK: - Playback State Persistence

extension AudioPlayerManager {
  private var playbackStateURL: URL {
    let documentsDirectory = FileManager.default.urls(for: .documentDirectory,
                                                      in: .userDomainMask).first!
    return documentsDirectory.appendingPathComponent("PlaybackState.json")
  }
    
  /// 获取指定歌曲的专辑封面（用于轮播图）
  func getArtworkForTrack(_ track: AudioFileInfo) -> UIImage? {
    let cacheKey = track.url.absoluteString
        
    // 检查缓存
    if let cachedArtwork = artworkCache[cacheKey] {
      return cachedArtwork
    }
        
    // 如果是当前播放的歌曲，返回已加载的封面
    if currentTrack?.id == track.id, let artwork = albumArtwork {
      artworkCache[cacheKey] = artwork
      return artwork
    }
    
    // 🔧 修改：只对Navidrome流媒体获取封面
    if isNavidromeStream(track.url) {
      loadNavidromeCoverArtForTrack(track)
    } else {
      print("⚠️ 非Navidrome文件，不获取封面")
    }
        
    return nil // 异步加载，暂时返回nil
  }
  
  private func loadNavidromeCoverArtForTrack(_ track: AudioFileInfo) {
    guard let client = navidromeClient,
          client.isAuthenticated,
          let songId = extractSongId(from: track.url)
    else {
      return
    }
    
    let cacheKey = track.url.absoluteString
    
    // 获取封面URL
    guard let coverURL = client.getCoverArtURL(for: songId, size: 200) else {
      return
    }
    
    Task {
      do {
        let (data, response) = try await URLSession.shared.data(from: coverURL)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200,
              let image = UIImage(data: data)
        else {
          return
        }
        
        await MainActor.run {
          // 缓存封面
          self.artworkCache[cacheKey] = image
          self.artworkCacheUpdated.toggle() // 触发视图更新
        }
        
      } catch {
        print("加载Navidrome轮播封面失败: \(error.localizedDescription)")
      }
    }
  }
  
  /// 保存当前播放状态
  func savePlaybackState() {
    let state = PlaybackState(
      playlist: playlist,
      currentTrackIndex: currentTrackIndex,
      currentTime: currentTime,
      volume: volume,
      lastSaveTime: Date(),
      isShuffleEnabled: isShuffleEnabled,
      repeatMode: repeatMode,
      originalPlaylist: originalPlaylist
    )
        
    do {
      let data = try JSONEncoder().encode(state)
      try data.write(to: playbackStateURL)
      print("播放状态已保存: \(playlist.count)首歌曲, 当前第\(currentTrackIndex + 1)首, 播放进度\(String(format: "%.1f", currentTime))秒")
    } catch {
      print("保存播放状态失败: \(error.localizedDescription)")
    }
  }
    
  /// 恢复播放状态
  func restorePlaybackState() {
    guard FileManager.default.fileExists(atPath: playbackStateURL.path) else {
      print("播放状态文件不存在，使用默认设置")
      return
    }
        
    do {
      let data = try Data(contentsOf: playbackStateURL)
      let state = try JSONDecoder().decode(PlaybackState.self, from: data)
            
      // 验证播放列表中的文件是否仍然存在（网络URL总是有效）
      let validPlaylist = state.playlist.filter { track in
        // 网络URL不需要验证文件存在性
        if track.url.scheme == "http" || track.url.scheme == "https" {
          return true
        }
        // 本地文件需要验证存在性
        return FileManager.default.fileExists(atPath: track.url.path)
      }
            
      // 只有在有有效的播放列表时才恢复状态
      if !validPlaylist.isEmpty {
        playlist = validPlaylist
                
        // 确保 currentTrackIndex 在有效范围内
        currentTrackIndex = min(state.currentTrackIndex, validPlaylist.count - 1)
                
        // 恢复音量设置
        volume = state.volume
        
        // 恢复乱序和循环播放状态
        isShuffleEnabled = state.isShuffleEnabled
        repeatMode = state.repeatMode
        originalPlaylist = state.originalPlaylist
                
        // 如果有当前播放的曲目，设置相关信息但不开始播放
        if currentTrackIndex < playlist.count {
          let currentFile = playlist[currentTrackIndex]
          currentTrack = currentFile
          currentTime = state.currentTime
          
          // 优先使用队列中保存的时长
          if currentFile.duration > 0 {
            duration = currentFile.duration
            print("✅ 恢复时使用队列中保存的时长: \(String(format: "%.1f", currentFile.duration))秒")
          } else {
            // 只有在队列中没有时长信息时才获取
            print("⚠️ 队列中无时长信息，需要获取时长")
            
            // 只对本地文件获取音频时长，避免网络请求
            if currentFile.url.scheme != "http", currentFile.url.scheme != "https" {
              Task {
                do {
                  let asset = AVURLAsset(url: currentFile.url)
                  let duration = try await asset.load(.duration)
                  let durationInSeconds = CMTimeGetSeconds(duration)
                                  
                  DispatchQueue.main.async {
                    self.duration = durationInSeconds
                  }
                } catch {
                  print("获取音频时长失败: \(error.localizedDescription)")
                }
              }
            } else {
              print("⚠️ 网络流媒体跳过时长预加载，但会获取封面")
            }
          }
                    
          // 异步获取专辑封面（包括Navidrome封面）
          DispatchQueue.global(qos: .background).async { [weak self] in
            self?.extractAlbumArtwork(from: currentFile.url)
          }
        }
                
        print("播放状态已恢复: \(playlist.count)首歌曲, 当前第\(currentTrackIndex + 1)首")
        print("恢复时间: \(String(format: "%.1f", currentTime))秒, 音量: \(String(format: "%.1f", volume))")
      } else {
        print("播放列表中的文件都不存在，清空播放状态")
        clearPlaybackState()
      }
            
    } catch {
      print("恢复播放状态失败: \(error.localizedDescription)")
    }
  }
    
  /// 清空播放状态
  func clearPlaybackState() {
    playlist.removeAll()
    originalPlaylist.removeAll()
    shuffledIndices.removeAll()
    currentTrackIndex = 0
    currentTime = 0
    currentTrack = nil
    duration = 0
    albumArtwork = nil
    isShuffleEnabled = false
    repeatMode = .off
        
    // 删除保存的状态文件
    try? FileManager.default.removeItem(at: playbackStateURL)
    print("播放状态已清空")
  }
    
  /// 自动保存播放状态（在关键操作后调用）
  private func autoSavePlaybackState() {
    // 使用后台队列保存，避免阻塞主线程
    DispatchQueue.global(qos: .utility).async { [weak self] in
      self?.savePlaybackState()
    }
  }
    
  /// 恢复播放（包括播放进度）
  func resumeFromSavedState() {
    guard let currentTrack = currentTrack else {
      // 如果没有当前曲目但有播放列表，播放第一首
      if !playlist.isEmpty {
        playCurrentTrack()
      }
      return
    }
    
    // 检查是否是网络流媒体
    if currentTrack.url.scheme == "http" || currentTrack.url.scheme == "https" {
      // 对于网络流媒体，等待下载完成后播放
      waitForDownloadAndPlay(file: currentTrack)
    } else {
      // 对于本地文件，使用AVAudioPlayer
      resumeLocalPlayback(for: currentTrack)
    }
  }
  
  private func resumeLocalPlayback(for track: AudioFileInfo) {
    do {
      // 停止当前播放
      stop()
            
      // 创建新的音频播放器
      audioPlayer = try AVAudioPlayer(contentsOf: track.url)
      audioPlayer?.delegate = self
      audioPlayer?.volume = Float(volume)
      audioPlayer?.prepareToPlay()
      
      // 获取专辑封面
      extractAlbumArtwork(from: track.url)
            
      // 恢复到保存的播放位置
      if currentTime > 0, currentTime < duration {
        audioPlayer?.currentTime = currentTime
      }
            
      // 开始播放
      if audioPlayer?.play() == true {
        isPlaying = true
        startTimer()
        updateNowPlayingInfo()
        print("恢复本地播放: \(track.name) 从 \(String(format: "%.1f", currentTime))秒开始")
      }
            
    } catch {
      print("恢复本地播放失败: \(error.localizedDescription)")
    }
  }

  // MARK: - 辅助方法
  
  /// 从Navidrome URL中提取歌曲ID
  private func extractSongIdFromURL(_ url: URL) -> String? {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let queryItems = components.queryItems
    else {
      return nil
    }
    
    // 查找id参数
    for item in queryItems {
      if item.name == "id", let value = item.value {
        return value
      }
    }
    
    return nil
  }
  
  // 下载相关功能已删除
}

// MARK: - 音频下载和缓存功能已完全移除

// 现在只支持纯流媒体播放，不进行任何本地下载或缓存
