//
//  SystemVolumeSlider.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import AVFoundation
import MediaPlayer
import SwiftUI

struct SystemVolumeSlider: View {
  @Binding var volume: Double
  @State private var volumeView: MPVolumeView?
    
  var body: some View {
    HStack(spacing: 15) {
      Image(systemName: "speaker.fill")
        .font(.caption)
        .foregroundColor(.secondary)
            
      Slider(value: self.$volume, in: 0 ... 1) { editing in
        if !editing {
          self.setSystemVolume(self.volume)
        }
      }
      .tint(.primary)
      .onAppear {
        self.setupVolumeView()
      }
            
      Image(systemName: "speaker.wave.3.fill")
        .font(.caption)
        .foregroundColor(.secondary)
    }
  }
    
  private func setupVolumeView() {
    // 创建一个隐藏的MPVolumeView用于控制系统音量
    let volView = MPVolumeView(frame: CGRect(x: -1000, y: -1000, width: 1, height: 1))
    volView.showsVolumeSlider = true
    volView.isHidden = false
        
    // 将其添加到某个窗口中（但位置在屏幕外）
    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
       let window = windowScene.windows.first
    {
      window.addSubview(volView)
      self.volumeView = volView
    }
  }
    
  private func setSystemVolume(_ newVolume: Double) {
    guard let volumeView = volumeView else { return }
        
    // 查找音量滑块
    for subview in volumeView.subviews {
      if let slider = subview as? UISlider {
        DispatchQueue.main.async {
          slider.value = Float(newVolume)
        }
        break
      }
    }
  }
}

#Preview {
  @Previewable @State var volume = 0.5
  return SystemVolumeSlider(volume: $volume)
    .padding()
}
