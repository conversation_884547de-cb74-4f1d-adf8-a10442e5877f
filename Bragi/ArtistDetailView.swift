//
//  ArtistDetailView.swift
//  Bragi
//
//  Created by <PERSON> on 6/13/25.
//

import Foundation
import SwiftUI

// MARK: - 艺术家详情页面

struct ArtistDetailView: View {
  let artist: NavidromeArtist
  let apiClient: NavidromeAPIClient
  
  @Environment(\.dismiss) private var dismiss
  @State private var albums: [NavidromeAlbum] = []
  @State private var isLoading = false
  @State private var showingAlert = false
  @State private var alertMessage = ""
  @State private var searchText = ""
  
  var filteredAlbums: [NavidromeAlbum] {
    if searchText.isEmpty {
      return albums
    } else {
      return albums.filter { album in
        album.displayName.localizedCaseInsensitiveContains(searchText) ||
          (album.genre?.localizedCaseInsensitiveContains(searchText) ?? false)
      }
    }
  }
  
  var body: some View {
    VStack(spacing: 0) {
      // 艺术家头部信息
      artistHeaderView
      
      Divider()
      
      if isLoading {
        loadingView
      } else if albums.isEmpty {
        emptyStateView
      } else {
        albumListView
      }
    }
    .navigationTitle(artist.displayName)
    .navigationBarTitleDisplayMode(.large)
    .searchable(text: $searchText, prompt: "搜索专辑")
    .toolbar {
      ToolbarItem(placement: .navigationBarTrailing) {
        Button(action: refreshAlbums) {
          Image(systemName: "arrow.clockwise")
            .font(.title2)
        }
        .disabled(isLoading)
      }
    }
    .alert("提示", isPresented: $showingAlert) {
      Button("确定", role: .cancel) {}
    } message: {
      Text(alertMessage)
    }
    .onAppear {
      Task {
        await loadAlbums()
      }
    }
  }
  
  private var artistHeaderView: some View {
    HStack(spacing: 16) {
      // 艺术家头像
      if let coverArt = artist.coverArt {
        CoverArtView(
          coverArtId: coverArt,
          apiClient: apiClient,
          size: 100,
          cornerRadius: 50,
          showProgress: true
        )
      } else {
        Circle()
          .fill(
            LinearGradient(
              gradient: Gradient(colors: [Color.blue.opacity(0.7), Color.purple.opacity(0.5)]),
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 100, height: 100)
          .overlay(
            Image(systemName: "person.circle")
              .font(.system(size: 50))
              .foregroundColor(.white)
          )
      }
      
      // 艺术家信息
      VStack(alignment: .leading, spacing: 8) {
        Text(artist.displayName)
          .font(.title2)
          .fontWeight(.bold)
          .foregroundColor(.primary)
          .lineLimit(2)
        
        Text(artist.albumCountString)
          .font(.title3)
          .foregroundColor(.secondary)
        
        if let starred = artist.starred {
          HStack {
            Image(systemName: "star.fill")
              .font(.caption)
              .foregroundColor(.yellow)
            
            Text("已收藏")
              .font(.caption)
              .foregroundColor(.secondary)
          }
        }
        
        Text("点击专辑查看详情")
          .font(.caption2)
          .foregroundColor(.secondary)
      }
      
      Spacer()
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
  }
  
  private var loadingView: some View {
    VStack(spacing: 20) {
      ProgressView()
        .scaleEffect(1.5)
      
      Text("正在加载专辑...")
        .font(.headline)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var emptyStateView: some View {
    VStack(spacing: 20) {
      Image(systemName: "opticaldisc")
        .font(.system(size: 60))
        .foregroundColor(.secondary)
      
      Text("暂无专辑")
        .font(.title2)
        .fontWeight(.medium)
        .foregroundColor(.primary)
      
      Text("这位艺术家还没有专辑")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
      
      Button(action: refreshAlbums) {
        HStack {
          Image(systemName: "arrow.clockwise")
          Text("刷新")
        }
        .foregroundColor(.white)
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .background(
          LinearGradient(
            gradient: Gradient(colors: [Color.purple, Color.blue]),
            startPoint: .leading,
            endPoint: .trailing
          )
        )
        .cornerRadius(25)
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
  
  private var albumListView: some View {
    List {
      ForEach(filteredAlbums) { album in
        NavigationLink(destination: AlbumDetailView(album: album, apiClient: apiClient, onDismissLibrary: nil)) {
          NavidromeAlbumRow(album: album, apiClient: apiClient) {
            // 点击行为由NavigationLink处理
          }
        }
        .buttonStyle(PlainButtonStyle())
      }
    }
    .listStyle(PlainListStyle())
    .refreshable {
      await loadAlbums()
    }
  }
  
  private func loadAlbums() async {
    await MainActor.run {
      isLoading = true
    }
    
    do {
      let fetchedAlbums = try await apiClient.fetchArtistAlbums(artistId: artist.id)
      await MainActor.run {
        // 按年份和名称排序
        self.albums = fetchedAlbums.sorted { album1, album2 in
          if let year1 = album1.year, let year2 = album2.year {
            if year1 != year2 {
              return year1 > year2 // 新专辑在前
            }
          } else if album1.year != nil {
            return true
          } else if album2.year != nil {
            return false
          }
          return album1.displayName < album2.displayName
        }
        self.isLoading = false
      }
    } catch {
      await MainActor.run {
        self.isLoading = false
        self.alertMessage = "加载艺术家专辑失败: \(error.localizedDescription)"
        self.showingAlert = true
      }
    }
  }
  
  private func refreshAlbums() {
    Task {
      await loadAlbums()
    }
  }
}

#Preview {
  NavigationView {
    ArtistDetailView(
      artist: NavidromeArtist(
        id: "1",
        name: "示例艺术家",
        coverArt: nil,
        albumCount: 5,
        starred: nil
      ),
      apiClient: NavidromeAPIClient()
    )
  }
}
