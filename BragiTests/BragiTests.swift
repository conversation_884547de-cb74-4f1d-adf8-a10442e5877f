//
//  BragiTests.swift
//  BragiTests
//
//  Created by <PERSON> on 6/13/25.
//

@testable import Bragi
import Testing

struct BragiTests {
  @Test func example() async throws {
    // Write your test here and use APIs like `#expect(...)` to check expected conditions.
  }

  @Test func testFavoriteStatusManager() async throws {
    // 测试收藏状态管理器
    let manager = FavoriteStatusManager.shared

    // 清空缓存
    manager.clearCache()

    // 测试初始状态
    #expect(manager.isFavorite(songId: "test-song-1") == false)
    #expect(manager.isFavorite(songId: "test-song-2") == false)

    // 测试更新收藏状态
    manager.updateFavoriteStatus(songId: "test-song-1", isFavorite: true)
    #expect(manager.isFavorite(songId: "test-song-1") == true)
    #expect(manager.isFavorite(songId: "test-song-2") == false)

    // 测试切换收藏状态
    let newStatus = manager.toggleFavoriteStatus(songId: "test-song-2")
    #expect(newStatus == true)
    #expect(manager.isFavorite(songId: "test-song-2") == true)

    // 再次切换
    let newStatus2 = manager.toggleFavoriteStatus(songId: "test-song-2")
    #expect(newStatus2 == false)
    #expect(manager.isFavorite(songId: "test-song-2") == false)

    // 测试获取收藏歌曲ID集合
    let favoriteIds = manager.getFavoriteSongIds()
    #expect(favoriteIds.contains("test-song-1"))
    #expect(!favoriteIds.contains("test-song-2"))

    // 测试批量更新
    let testSongs = [
      NavidromeSong(
        id: "batch-song-1",
        title: "Test Song 1",
        artist: "Test Artist",
        album: "Test Album",
        track: 1,
        year: 2024,
        genre: "Test",
        duration: 180,
        size: 1024,
        suffix: "mp3",
        albumId: "test-album",
        artistId: "test-artist",
        path: "/test/path",
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        coverArt: nil,
        starred: "2024-01-01T00:00:00.000Z" // 已收藏
      ),
      NavidromeSong(
        id: "batch-song-2",
        title: "Test Song 2",
        artist: "Test Artist",
        album: "Test Album",
        track: 2,
        year: 2024,
        genre: "Test",
        duration: 200,
        size: 2048,
        suffix: "mp3",
        albumId: "test-album",
        artistId: "test-artist",
        path: "/test/path2",
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        coverArt: nil,
        starred: nil // 未收藏
      )
    ]

    manager.updateFavoriteStatuses(from: testSongs)
    #expect(manager.isFavorite(songId: "batch-song-1") == true)
    #expect(manager.isFavorite(songId: "batch-song-2") == false)

    // 清理
    manager.clearCache()
  }
}
